'''
    Author: AI Assistant
    Python Version: 3.9.7

    三管同心管机器人运动学模型 (嵌套结构)
    Three-tube Concentric Tube Robot Kinematics Model (Nested Structure)

    机器人结构:
    - 外管(最大): 长度55mm, 伸缩33mm, 曲率0 (直管)
    - 中管(中等): 长度105mm, 伸缩73mm, 曲率0 (直管，嵌套在外管内)
    - 内管(最小): 长度215mm, 伸缩175mm, 曲率1/143mm (弯曲管，嵌套在中管内)

    关键特性:
    1. 嵌套结构: 内管在中管内，中管在外管内
    2. 独立伸缩: 每根管可以独立伸出/缩回
    3. 独立旋转: 每根管可以绕同心轴独立旋转
    4. 柔性内管: 内管有预设曲率，可在直管内弯曲伸缩
'''

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import warnings

class ConcentricTubeRobot:
    """
    三管同心管机器人类 (嵌套结构)
    Three-tube Concentric Tube Robot Class (Nested Structure)
    """

    def __init__(self):
        """
        初始化三管同心管机器人参数
        Initialize three-tube concentric tube robot parameters
        """
        # 管子参数 (单位: mm)
        # Tube parameters (unit: mm)
        self.tube_params = {
            'outer': {
                'total_length': 55.0,        # 外管总长度 (mm)
                'max_extension': 33.0,       # 外管最大伸出长度 (mm)
                'curvature': 0.0,            # 外管曲率 (1/mm) - 直管
                'straight_length': 55.0 - 33.0,  # 基座内的直段长度
                'diameter': 6.0,             # 外管外径 (mm)
                'inner_diameter': 5.0        # 外管内径 (mm)
            },
            'middle': {
                'total_length': 105.0,       # 中管总长度 (mm)
                'max_extension': 73.0,       # 中管最大伸出长度 (mm)
                'curvature': 0.0,            # 中管曲率 (1/mm) - 直管
                'straight_length': 105.0 - 73.0,  # 基座内的直段长度
                'diameter': 4.0,             # 中管外径 (mm)
                'inner_diameter': 3.0        # 中管内径 (mm)
            },
            'inner': {
                'total_length': 215.0,       # 内管总长度 (mm)
                'max_extension': 175.0,      # 内管最大伸出长度 (mm)
                'curvature': 1.0/143.0,      # 内管曲率 (1/mm) - 弯曲管
                'straight_length': 215.0 - 175.0,  # 基座内的直段长度
                'diameter': 2.0,             # 内管外径 (mm)
                'inner_diameter': 1.0        # 内管内径 (mm)
            }
        }

        # 当前状态变量 (相对于基座的伸出长度)
        # Current state variables (extension length relative to base)
        self.current_extensions = {
            'outer': 0.0,    # 外管当前伸出长度 (0 到 max_extension)
            'middle': 0.0,   # 中管当前伸出长度 (0 到 max_extension)
            'inner': 0.0     # 内管当前伸出长度 (0 到 max_extension)
        }

        # 当前旋转角度 (绕同心轴旋转)
        # Current rotation angles (rotation around concentric axis)
        self.current_rotations = {
            'outer': 0.0,    # 外管旋转角度 (rad)
            'middle': 0.0,   # 中管旋转角度 (rad)
            'inner': 0.0     # 内管旋转角度 (rad)
        }
        
    def set_configuration(self, extensions: dict, rotations: dict):
        """
        设置机器人配置
        Set robot configuration

        Parameters:
        -----------
        extensions : dict
            各管的伸出长度 {'outer': float, 'middle': float, 'inner': float}
        rotations : dict
            各管的旋转角度 {'outer': float, 'middle': float, 'inner': float}
        """
        # 验证伸出长度限制
        for tube_name, ext in extensions.items():
            max_ext = self.tube_params[tube_name]['max_extension']
            if ext < 0 or ext > max_ext:
                warnings.warn(f"{tube_name} tube extension {ext}mm exceeds limits [0, {max_ext}]mm")
                extensions[tube_name] = np.clip(ext, 0, max_ext)

        self.current_extensions = extensions.copy()
        self.current_rotations = rotations.copy()
    
    def transformation_matrix(self, kappa: float, length: float, theta: float = 0.0) -> np.ndarray:
        """
        计算单段的齐次变换矩阵 (常曲率假设)
        Calculate homogeneous transformation matrix for a single segment (constant curvature)

        Parameters:
        -----------
        kappa : float
            曲率 (1/mm)
        length : float
            长度 (mm)
        theta : float
            旋转角度 (rad) - 绕z轴旋转

        Returns:
        --------
        T : np.ndarray
            4x4齐次变换矩阵
        """
        if abs(kappa) < 1e-6:  # 直线段
            # 直线段变换矩阵: 沿z轴平移，然后绕z轴旋转
            T = np.array([
                [np.cos(theta), -np.sin(theta), 0, 0],
                [np.sin(theta),  np.cos(theta), 0, 0],
                [0,              0,             1, length],
                [0,              0,             0, 1]
            ])
        else:
            # 弯曲段变换矩阵 (常曲率模型)
            phi = kappa * length  # 弯曲角度 (rad)
            radius = 1.0 / kappa  # 曲率半径 (mm)

            # 先在xz平面内弯曲，然后绕z轴旋转theta角度
            # 弯曲后的末端位置
            x_bend = radius * np.sin(phi)
            z_bend = radius * (1 - np.cos(phi))

            # 应用旋转
            T = np.array([
                [np.cos(theta), -np.sin(theta), 0, x_bend * np.cos(theta)],
                [np.sin(theta),  np.cos(theta), 0, x_bend * np.sin(theta)],
                [0,              0,             1, z_bend],
                [0,              0,             0, 1]
            ])

        return T
    
    def forward_kinematics(self) -> Tuple[np.ndarray, List[np.ndarray], dict]:
        """
        计算前向运动学 (嵌套结构)
        Calculate forward kinematics (nested structure)

        关键思想:
        1. 确定最外层伸出的管子
        2. 计算各管的有效长度和形状
        3. 组合变换得到最终末端位姿

        Returns:
        --------
        tip_pose : np.ndarray
            末端位姿 4x4矩阵
        segment_poses : List[np.ndarray]
            各段末端位姿列表
        tube_info : dict
            各管的详细信息
        """
        # 获取各管的伸出长度
        ext_outer = self.current_extensions['outer']
        ext_middle = self.current_extensions['middle']
        ext_inner = self.current_extensions['inner']

        # 获取各管的旋转角度
        rot_outer = self.current_rotations['outer']
        rot_middle = self.current_rotations['middle']
        rot_inner = self.current_rotations['inner']

        # 确定最外层伸出的管子和有效长度
        max_extension = max(ext_outer, ext_middle, ext_inner)

        segment_poses = []
        tube_info = {}
        T_total = np.eye(4)

        if max_extension <= 0:
            # 没有管子伸出
            return T_total, segment_poses, tube_info

        # 分段计算: 从基座到最远端
        # 第一段: 从基座到第一个管子末端 (最短的伸出长度)
        min_extension = min([ext for ext in [ext_outer, ext_middle, ext_inner] if ext > 0])

        if min_extension > 0:
            # 在这一段，所有伸出的管子都存在
            # 计算组合曲率和旋转
            combined_kappa, combined_rotation = self._calculate_combined_properties(
                ext_outer, ext_middle, ext_inner,
                rot_outer, rot_middle, rot_inner,
                min_extension
            )

            # 第一段变换
            T_segment1 = self.transformation_matrix(combined_kappa, min_extension, combined_rotation)
            T_total = T_total @ T_segment1
            segment_poses.append(T_total.copy())
            tube_info['segment_1'] = {
                'length': min_extension,
                'curvature': combined_kappa,
                'rotation': combined_rotation,
                'active_tubes': [name for name in ['outer', 'middle', 'inner']
                               if self.current_extensions[name] > 0]
            }

        # 后续段: 处理剩余的伸出长度
        remaining_extensions = {
            'outer': max(0, ext_outer - min_extension),
            'middle': max(0, ext_middle - min_extension),
            'inner': max(0, ext_inner - min_extension)
        }

        # 按长度排序处理剩余段
        remaining_sorted = sorted(remaining_extensions.items(),
                                key=lambda x: x[1], reverse=True)

        current_length = min_extension
        for tube_name, remaining_length in remaining_sorted:
            if remaining_length > 0:
                # 计算这一段的属性
                segment_kappa, segment_rotation = self._calculate_segment_properties(
                    tube_name, current_length, remaining_length
                )

                # 变换矩阵
                T_segment = self.transformation_matrix(segment_kappa, remaining_length, segment_rotation)
                T_total = T_total @ T_segment
                segment_poses.append(T_total.copy())

                tube_info[f'segment_{tube_name}'] = {
                    'length': remaining_length,
                    'curvature': segment_kappa,
                    'rotation': segment_rotation,
                    'active_tube': tube_name
                }

                current_length += remaining_length

        return T_total, segment_poses, tube_info

    def _calculate_combined_properties(self, ext_outer: float, ext_middle: float, ext_inner: float,
                                     rot_outer: float, rot_middle: float, rot_inner: float,
                                     segment_length: float) -> Tuple[float, float]:
        """
        计算多管重叠段的组合曲率和旋转
        Calculate combined curvature and rotation for overlapping tubes

        Parameters:
        -----------
        ext_outer, ext_middle, ext_inner : float
            各管伸出长度
        rot_outer, rot_middle, rot_inner : float
            各管旋转角度
        segment_length : float
            当前段长度

        Returns:
        --------
        combined_kappa : float
            组合曲率
        combined_rotation : float
            组合旋转角度
        """
        # 确定哪些管在这一段是活跃的
        active_tubes = []
        if ext_outer >= segment_length:
            active_tubes.append('outer')
        if ext_middle >= segment_length:
            active_tubes.append('middle')
        if ext_inner >= segment_length:
            active_tubes.append('inner')

        if not active_tubes:
            return 0.0, 0.0

        # 计算组合曲率 (基于刚度加权平均)
        # 简化模型: 假设管子刚度与直径的4次方成正比
        total_stiffness = 0.0
        weighted_curvature = 0.0
        weighted_rotation = 0.0

        for tube_name in active_tubes:
            tube = self.tube_params[tube_name]
            # 简化刚度模型 (实际应该考虑材料属性)
            stiffness = (tube['diameter'] / 2.0) ** 4  # 简化的弯曲刚度

            total_stiffness += stiffness
            weighted_curvature += tube['curvature'] * stiffness

            # 旋转角度加权
            if tube_name == 'outer':
                weighted_rotation += rot_outer * stiffness
            elif tube_name == 'middle':
                weighted_rotation += rot_middle * stiffness
            else:  # inner
                weighted_rotation += rot_inner * stiffness

        if total_stiffness > 0:
            combined_kappa = weighted_curvature / total_stiffness
            combined_rotation = weighted_rotation / total_stiffness
        else:
            combined_kappa = 0.0
            combined_rotation = 0.0

        return combined_kappa, combined_rotation

    def _calculate_segment_properties(self, tube_name: str, start_length: float,
                                    segment_length: float) -> Tuple[float, float]:
        """
        计算单管段的属性
        Calculate properties for a single tube segment

        Parameters:
        -----------
        tube_name : str
            管子名称
        start_length : float
            段起始位置
        segment_length : float
            段长度

        Returns:
        --------
        kappa : float
            曲率
        rotation : float
            旋转角度
        """
        tube = self.tube_params[tube_name]
        kappa = tube['curvature']

        if tube_name == 'outer':
            rotation = self.current_rotations['outer']
        elif tube_name == 'middle':
            rotation = self.current_rotations['middle']
        else:  # inner
            rotation = self.current_rotations['inner']

        return kappa, rotation

    def get_tip_position(self) -> np.ndarray:
        """
        获取末端位置
        Get tip position

        Returns:
        --------
        position : np.ndarray
            末端位置 [x, y, z] (mm)
        """
        tip_pose, _, _ = self.forward_kinematics()
        return tip_pose[:3, 3]

    def get_tip_orientation(self) -> np.ndarray:
        """
        获取末端姿态
        Get tip orientation

        Returns:
        --------
        orientation : np.ndarray
            末端姿态旋转矩阵 3x3
        """
        tip_pose, _, _ = self.forward_kinematics()
        return tip_pose[:3, :3]

    def get_robot_shape(self, n_points: int = 50) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        获取机器人完整形状点
        Get complete robot shape points

        Parameters:
        -----------
        n_points : int
            每段的点数

        Returns:
        --------
        x_points, y_points, z_points : np.ndarray
            机器人形状的坐标点
        """
        tip_pose, segment_poses, tube_info = self.forward_kinematics()

        x_points, y_points, z_points = [0], [0], [0]  # 起始点

        if not segment_poses:
            return np.array(x_points), np.array(y_points), np.array(z_points)

        # 为每个段生成详细的形状点
        T_current = np.eye(4)
        segment_idx = 0

        for segment_name, info in tube_info.items():
            length = info['length']
            kappa = info['curvature']
            rotation = info['rotation']

            # 生成这一段的点
            s_values = np.linspace(0, length, n_points)
            for s in s_values[1:]:  # 跳过起始点避免重复
                T_segment = self.transformation_matrix(kappa, s, rotation)
                T_point = T_current @ T_segment
                x_points.append(T_point[0, 3])
                y_points.append(T_point[1, 3])
                z_points.append(T_point[2, 3])

            # 更新累积变换
            T_segment_full = self.transformation_matrix(kappa, length, rotation)
            T_current = T_current @ T_segment_full
            segment_idx += 1

        return np.array(x_points), np.array(y_points), np.array(z_points)
    
    def jacobian_matrix(self, delta: float = 0.01) -> np.ndarray:
        """
        计算雅可比矩阵 (数值微分)
        Calculate Jacobian matrix (numerical differentiation)
        
        Parameters:
        -----------
        delta : float
            微分步长
            
        Returns:
        --------
        J : np.ndarray
            雅可比矩阵 (3x6) - [x,y,z] vs [ext_out, ext_mid, ext_in, rot_out, rot_mid, rot_in]
        """
        # 获取当前末端位置
        current_pos = self.get_tip_position()
        
        # 初始化雅可比矩阵
        J = np.zeros((3, 6))
        
        # 对各个自由度进行数值微分
        variables = [
            ('outer', 'extension'), ('middle', 'extension'), ('inner', 'extension'),
            ('outer', 'rotation'), ('middle', 'rotation'), ('inner', 'rotation')
        ]
        
        for i, (tube_name, var_type) in enumerate(variables):
            # 保存原始值
            if var_type == 'extension':
                original_val = self.current_extensions[tube_name]
                self.current_extensions[tube_name] += delta
            else:  # rotation
                original_val = self.current_rotations[tube_name]
                self.current_rotations[tube_name] += delta
            
            # 计算扰动后的位置
            perturbed_pos = self.get_tip_position()
            
            # 计算偏导数
            J[:, i] = (perturbed_pos - current_pos) / delta
            
            # 恢复原始值
            if var_type == 'extension':
                self.current_extensions[tube_name] = original_val
            else:
                self.current_rotations[tube_name] = original_val
        
        return J
    
    def workspace_analysis(self, n_samples: int = 1000) -> np.ndarray:
        """
        工作空间分析 (嵌套结构)
        Workspace analysis (nested structure)

        Parameters:
        -----------
        n_samples : int
            采样点数量

        Returns:
        --------
        workspace_points : np.ndarray
            工作空间点集 (n_samples, 3)
        """
        workspace_points = []

        # 保存当前配置
        original_ext = self.current_extensions.copy()
        original_rot = self.current_rotations.copy()

        for _ in range(n_samples):
            # 随机生成配置
            extensions = {
                'outer': np.random.uniform(0, self.tube_params['outer']['max_extension']),
                'middle': np.random.uniform(0, self.tube_params['middle']['max_extension']),
                'inner': np.random.uniform(0, self.tube_params['inner']['max_extension'])
            }

            rotations = {
                'outer': np.random.uniform(0, 2*np.pi),
                'middle': np.random.uniform(0, 2*np.pi),
                'inner': np.random.uniform(0, 2*np.pi)
            }

            # 设置配置并计算末端位置
            self.set_configuration(extensions, rotations)
            tip_pos = self.get_tip_position()
            workspace_points.append(tip_pos)

        # 恢复原始配置
        self.current_extensions = original_ext
        self.current_rotations = original_rot

        return np.array(workspace_points)
    
    def plot_robot_shape(self, ax=None, n_points: int = 50):
        """
        绘制机器人形状 (嵌套结构可视化)
        Plot robot shape (nested structure visualization)

        Parameters:
        -----------
        ax : matplotlib axis
            绘图轴
        n_points : int
            每段的绘制点数
        """
        if ax is None:
            _, ax = plt.subplots(figsize=(10, 8))

        # 获取机器人形状
        x_points, y_points, z_points = self.get_robot_shape(n_points)

        # 绘制主体形状
        ax.plot(x_points, y_points, 'b-', linewidth=3, label='Robot Shape', alpha=0.8)

        # 标记关键点
        ax.plot(0, 0, 'go', markersize=10, label='Base')  # 基座

        if len(x_points) > 1:
            ax.plot(x_points[-1], y_points[-1], 'ro', markersize=8, label='Tip')  # 末端

        # 显示各管的伸出状态
        info_text = []
        for tube_name in ['outer', 'middle', 'inner']:
            ext = self.current_extensions[tube_name]
            rot = self.current_rotations[tube_name]
            max_ext = self.tube_params[tube_name]['max_extension']
            info_text.append(f'{tube_name.capitalize()}: {ext:.1f}/{max_ext:.1f}mm, {np.degrees(rot):.1f}°')

        # 添加信息文本
        ax.text(0.02, 0.98, '\n'.join(info_text),
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=9)

        # 绘制各管的伸出范围指示
        self._draw_tube_indicators(ax)

        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_title('Concentric Tube Robot Configuration (Nested Structure)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

        return ax

    def _draw_tube_indicators(self, ax):
        """
        绘制各管的伸出范围指示
        Draw tube extension range indicators
        """
        colors = ['red', 'green', 'blue']
        alphas = [0.3, 0.4, 0.5]
        labels = ['Outer Range', 'Middle Range', 'Inner Range']

        for i, tube_name in enumerate(['outer', 'middle', 'inner']):
            max_ext = self.tube_params[tube_name]['max_extension']
            current_ext = self.current_extensions[tube_name]

            if max_ext > 0:
                # 绘制最大伸出范围的圆
                circle_max = plt.Circle((0, 0), max_ext,
                                      color=colors[i], alpha=alphas[i]/2,
                                      fill=False, linestyle='--', linewidth=1)
                ax.add_patch(circle_max)

                # 绘制当前伸出位置的圆
                if current_ext > 0:
                    circle_current = plt.Circle((0, 0), current_ext,
                                              color=colors[i], alpha=alphas[i],
                                              fill=False, linewidth=2)
                    ax.add_patch(circle_current)
