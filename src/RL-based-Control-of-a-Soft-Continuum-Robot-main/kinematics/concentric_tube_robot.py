'''
    Author: AI Assistant
    Python Version: 3.9.7
    
    三管同心管机器人运动学模型
    Three-tube Concentric Tube Robot Kinematics Model
    
    机器人参数:
    - 外管: 长度55mm, 伸缩33mm, 曲率0
    - 中管: 长度105mm, 伸缩73mm, 曲率0  
    - 内管: 长度215mm, 伸缩175mm, 曲率1/143mm
'''

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import warnings

class ConcentricTubeRobot:
    """
    三管同心管机器人类
    Three-tube Concentric Tube Robot Class
    """
    
    def __init__(self):
        """
        初始化三管同心管机器人参数
        Initialize three-tube concentric tube robot parameters
        """
        # 管子参数 (单位: mm)
        # Tube parameters (unit: mm)
        self.tube_params = {
            'outer': {
                'length': 55.0,          # 外管总长度 (mm)
                'extension': 33.0,       # 外管伸缩长度 (mm)
                'curvature': 0.0,        # 外管曲率 (1/mm)
                'straight_length': 55.0 - 33.0,  # 直段长度
                'curved_length': 33.0    # 弯曲段长度
            },
            'middle': {
                'length': 105.0,         # 中管总长度 (mm)
                'extension': 73.0,       # 中管伸缩长度 (mm)
                'curvature': 0.0,        # 中管曲率 (1/mm)
                'straight_length': 105.0 - 73.0,
                'curved_length': 73.0
            },
            'inner': {
                'length': 215.0,         # 内管总长度 (mm)
                'extension': 175.0,      # 内管伸缩长度 (mm)
                'curvature': 1.0/143.0,  # 内管曲率 (1/mm)
                'straight_length': 215.0 - 175.0,
                'curved_length': 175.0
            }
        }
        
        # 当前状态变量
        # Current state variables
        self.current_extensions = {
            'outer': 0.0,    # 当前外管伸出长度
            'middle': 0.0,   # 当前中管伸出长度
            'inner': 0.0     # 当前内管伸出长度
        }
        
        self.current_rotations = {
            'outer': 0.0,    # 外管旋转角度 (rad)
            'middle': 0.0,   # 中管旋转角度 (rad)
            'inner': 0.0     # 内管旋转角度 (rad)
        }
        
    def set_configuration(self, extensions: dict, rotations: dict):
        """
        设置机器人配置
        Set robot configuration
        
        Parameters:
        -----------
        extensions : dict
            各管的伸出长度 {'outer': float, 'middle': float, 'inner': float}
        rotations : dict
            各管的旋转角度 {'outer': float, 'middle': float, 'inner': float}
        """
        # 验证伸出长度限制
        for tube_name, ext in extensions.items():
            max_ext = self.tube_params[tube_name]['extension']
            if ext < 0 or ext > max_ext:
                warnings.warn(f"{tube_name} tube extension {ext}mm exceeds limits [0, {max_ext}]mm")
                extensions[tube_name] = np.clip(ext, 0, max_ext)
        
        self.current_extensions = extensions.copy()
        self.current_rotations = rotations.copy()
    
    def transformation_matrix(self, kappa: float, length: float, theta: float = 0.0) -> np.ndarray:
        """
        计算单段的齐次变换矩阵
        Calculate homogeneous transformation matrix for a single segment
        
        Parameters:
        -----------
        kappa : float
            曲率 (1/mm)
        length : float
            长度 (mm)
        theta : float
            旋转角度 (rad)
            
        Returns:
        --------
        T : np.ndarray
            4x4齐次变换矩阵
        """
        if abs(kappa) < 1e-6:  # 直线段
            # 直线段变换矩阵
            T = np.array([
                [np.cos(theta), -np.sin(theta), 0, 0],
                [np.sin(theta),  np.cos(theta), 0, length],
                [0,              0,             1, 0],
                [0,              0,             0, 1]
            ])
        else:
            # 弯曲段变换矩阵
            phi = kappa * length  # 弯曲角度
            radius = 1.0 / kappa  # 曲率半径
            
            T = np.array([
                [np.cos(phi + theta), -np.sin(phi + theta), 0, radius * (np.cos(theta) - np.cos(phi + theta))],
                [np.sin(phi + theta),  np.cos(phi + theta), 0, radius * (np.sin(theta) - np.sin(phi + theta))],
                [0,                    0,                   1, 0],
                [0,                    0,                   0, 1]
            ])
        
        return T
    
    def forward_kinematics(self) -> Tuple[np.ndarray, List[np.ndarray]]:
        """
        计算前向运动学
        Calculate forward kinematics
        
        Returns:
        --------
        tip_pose : np.ndarray
            末端位姿 4x4矩阵
        segment_poses : List[np.ndarray]
            各段末端位姿列表
        """
        segment_poses = []
        T_total = np.eye(4)  # 累积变换矩阵
        
        # 按照从外到内的顺序计算各管段
        tubes = ['outer', 'middle', 'inner']
        
        for tube_name in tubes:
            tube = self.tube_params[tube_name]
            extension = self.current_extensions[tube_name]
            rotation = self.current_rotations[tube_name]
            
            if extension > 0:
                # 计算该管的变换矩阵
                T_tube = self.transformation_matrix(
                    kappa=tube['curvature'],
                    length=extension,
                    theta=rotation
                )
                
                # 累积变换
                T_total = T_total @ T_tube
                segment_poses.append(T_total.copy())
        
        return T_total, segment_poses
    
    def get_tip_position(self) -> np.ndarray:
        """
        获取末端位置
        Get tip position
        
        Returns:
        --------
        position : np.ndarray
            末端位置 [x, y, z] (mm)
        """
        tip_pose, _ = self.forward_kinematics()
        return tip_pose[:3, 3]
    
    def get_tip_orientation(self) -> np.ndarray:
        """
        获取末端姿态
        Get tip orientation
        
        Returns:
        --------
        orientation : np.ndarray
            末端姿态旋转矩阵 3x3
        """
        tip_pose, _ = self.forward_kinematics()
        return tip_pose[:3, :3]
    
    def jacobian_matrix(self, delta: float = 0.01) -> np.ndarray:
        """
        计算雅可比矩阵 (数值微分)
        Calculate Jacobian matrix (numerical differentiation)
        
        Parameters:
        -----------
        delta : float
            微分步长
            
        Returns:
        --------
        J : np.ndarray
            雅可比矩阵 (3x6) - [x,y,z] vs [ext_out, ext_mid, ext_in, rot_out, rot_mid, rot_in]
        """
        # 获取当前末端位置
        current_pos = self.get_tip_position()
        
        # 初始化雅可比矩阵
        J = np.zeros((3, 6))
        
        # 对各个自由度进行数值微分
        variables = [
            ('outer', 'extension'), ('middle', 'extension'), ('inner', 'extension'),
            ('outer', 'rotation'), ('middle', 'rotation'), ('inner', 'rotation')
        ]
        
        for i, (tube_name, var_type) in enumerate(variables):
            # 保存原始值
            if var_type == 'extension':
                original_val = self.current_extensions[tube_name]
                self.current_extensions[tube_name] += delta
            else:  # rotation
                original_val = self.current_rotations[tube_name]
                self.current_rotations[tube_name] += delta
            
            # 计算扰动后的位置
            perturbed_pos = self.get_tip_position()
            
            # 计算偏导数
            J[:, i] = (perturbed_pos - current_pos) / delta
            
            # 恢复原始值
            if var_type == 'extension':
                self.current_extensions[tube_name] = original_val
            else:
                self.current_rotations[tube_name] = original_val
        
        return J
    
    def workspace_analysis(self, n_samples: int = 1000) -> np.ndarray:
        """
        工作空间分析
        Workspace analysis
        
        Parameters:
        -----------
        n_samples : int
            采样点数量
            
        Returns:
        --------
        workspace_points : np.ndarray
            工作空间点集 (n_samples, 3)
        """
        workspace_points = []
        
        # 保存当前配置
        original_ext = self.current_extensions.copy()
        original_rot = self.current_rotations.copy()
        
        for _ in range(n_samples):
            # 随机生成配置
            extensions = {
                'outer': np.random.uniform(0, self.tube_params['outer']['extension']),
                'middle': np.random.uniform(0, self.tube_params['middle']['extension']),
                'inner': np.random.uniform(0, self.tube_params['inner']['extension'])
            }
            
            rotations = {
                'outer': np.random.uniform(0, 2*np.pi),
                'middle': np.random.uniform(0, 2*np.pi),
                'inner': np.random.uniform(0, 2*np.pi)
            }
            
            # 设置配置并计算末端位置
            self.set_configuration(extensions, rotations)
            tip_pos = self.get_tip_position()
            workspace_points.append(tip_pos)
        
        # 恢复原始配置
        self.current_extensions = original_ext
        self.current_rotations = original_rot
        
        return np.array(workspace_points)
    
    def plot_robot_shape(self, ax=None, n_points: int = 50):
        """
        绘制机器人形状
        Plot robot shape
        
        Parameters:
        -----------
        ax : matplotlib axis
            绘图轴
        n_points : int
            每段的绘制点数
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 8))
        
        # 计算各段的形状点
        T_current = np.eye(4)
        colors = ['red', 'green', 'blue']  # 外管、中管、内管
        labels = ['Outer Tube', 'Middle Tube', 'Inner Tube']
        
        tubes = ['outer', 'middle', 'inner']
        
        for i, tube_name in enumerate(tubes):
            tube = self.tube_params[tube_name]
            extension = self.current_extensions[tube_name]
            rotation = self.current_rotations[tube_name]
            
            if extension > 0:
                # 生成该段的形状点
                s_values = np.linspace(0, extension, n_points)
                x_points, y_points = [], []
                
                for s in s_values:
                    T_segment = self.transformation_matrix(
                        kappa=tube['curvature'],
                        length=s,
                        theta=rotation
                    )
                    T_point = T_current @ T_segment
                    x_points.append(T_point[0, 3])
                    y_points.append(T_point[1, 3])
                
                # 绘制该段
                ax.plot(x_points, y_points, color=colors[i], 
                       linewidth=3-i*0.5, label=labels[i], alpha=0.8)
                
                # 更新累积变换
                T_tube = self.transformation_matrix(
                    kappa=tube['curvature'],
                    length=extension,
                    theta=rotation
                )
                T_current = T_current @ T_tube
        
        # 标记末端位置
        tip_pos = self.get_tip_position()
        ax.plot(tip_pos[0], tip_pos[1], 'ko', markersize=8, label='Tip')
        
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_title('Concentric Tube Robot Configuration')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        return ax
