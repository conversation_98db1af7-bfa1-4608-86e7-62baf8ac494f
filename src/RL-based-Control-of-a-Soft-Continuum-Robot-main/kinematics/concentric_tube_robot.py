'''
    Author: AI Assistant
    Python Version: 3.9.7

    三管同心管机器人运动学模型 (嵌套结构)
    Three-tube Concentric Tube Robot Kinematics Model (Nested Structure)

    机器人结构:
    - 外管(最大): 长度55mm, 伸缩33mm, 曲率0 (直管)
    - 中管(中等): 长度105mm, 伸缩73mm, 曲率0 (直管，嵌套在外管内)
    - 内管(最小): 长度215mm, 伸缩175mm, 曲率1/143mm (弯曲管，嵌套在中管内)

    关键特性:
    1. 嵌套结构: 内管在中管内，中管在外管内
    2. 独立伸缩: 每根管可以独立伸出/缩回
    3. 独立旋转: 每根管可以绕同心轴独立旋转
    4. 柔性内管: 内管有预设曲率，可在直管内弯曲伸缩
'''

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional
import warnings

class ConcentricTubeRobot:
    """
    三管同心管机器人类 (嵌套结构)
    Three-tube Concentric Tube Robot Class (Nested Structure)
    """

    def __init__(self):
        """
        初始化三管同心管机器人参数
        Initialize three-tube concentric tube robot parameters
        """
        # 管子参数 (单位: mm)
        # Tube parameters (unit: mm)
        self.tube_params = {
            'outer': {
                'total_length': 55.0,        # 外管总长度 (mm)
                'max_extension': 33.0,       # 外管最大伸出长度 (mm)
                'curvature': 0.0,            # 外管曲率 (1/mm) - 直管
                'straight_length': 55.0 - 33.0,  # 基座内的直段长度
                'diameter': 6.0,             # 外管外径 (mm)
                'inner_diameter': 5.0        # 外管内径 (mm)
            },
            'middle': {
                'total_length': 105.0,       # 中管总长度 (mm)
                'max_extension': 73.0,       # 中管最大伸出长度 (mm)
                'curvature': 0.0,            # 中管曲率 (1/mm) - 直管
                'straight_length': 105.0 - 73.0,  # 基座内的直段长度
                'diameter': 4.0,             # 中管外径 (mm)
                'inner_diameter': 3.0        # 中管内径 (mm)
            },
            'inner': {
                'total_length': 215.0,       # 内管总长度 (mm)
                'max_extension': 175.0,      # 内管最大伸出长度 (mm)
                'curvature': 1.0/143.0,      # 内管曲率 (1/mm) - 弯曲管
                'straight_length': 215.0 - 175.0,  # 基座内的直段长度
                'diameter': 2.0,             # 内管外径 (mm)
                'inner_diameter': 1.0        # 内管内径 (mm)
            }
        }

        # 当前状态变量 (相对于基座的伸出长度)
        # Current state variables (extension length relative to base)
        self.current_extensions = {
            'outer': 0.0,    # 外管当前伸出长度 (0 到 max_extension)
            'middle': 0.0,   # 中管当前伸出长度 (0 到 max_extension)
            'inner': 0.0     # 内管当前伸出长度 (0 到 max_extension)
        }

        # 当前旋转角度 (绕同心轴旋转)
        # Current rotation angles (rotation around concentric axis)
        self.current_rotations = {
            'outer': 0.0,    # 外管旋转角度 (rad)
            'middle': 0.0,   # 中管旋转角度 (rad)
            'inner': 0.0     # 内管旋转角度 (rad)
        }
        
    def set_configuration(self, extensions: dict, rotations: dict):
        """
        设置机器人配置
        Set robot configuration

        Parameters:
        -----------
        extensions : dict
            各管的伸出长度 {'outer': float, 'middle': float, 'inner': float}
        rotations : dict
            各管的旋转角度 {'outer': float, 'middle': float, 'inner': float}
        """
        # 验证伸出长度限制
        for tube_name, ext in extensions.items():
            max_ext = self.tube_params[tube_name]['max_extension']
            if ext < 0 or ext > max_ext:
                warnings.warn(f"{tube_name} tube extension {ext}mm exceeds limits [0, {max_ext}]mm")
                extensions[tube_name] = np.clip(ext, 0, max_ext)

        self.current_extensions = extensions.copy()
        self.current_rotations = rotations.copy()
    
    def transformation_matrix(self, kappa: float, length: float, theta: float = 0.0) -> np.ndarray:
        """
        计算单段的齐次变换矩阵 (常曲率假设)
        Calculate homogeneous transformation matrix for a single segment (constant curvature)

        Parameters:
        -----------
        kappa : float
            曲率 (1/mm)
        length : float
            长度 (mm)
        theta : float
            旋转角度 (rad) - 绕z轴旋转

        Returns:
        --------
        T : np.ndarray
            4x4齐次变换矩阵
        """
        if abs(kappa) < 1e-6:  # 直线段
            # 直线段变换矩阵: 沿z轴平移，然后绕z轴旋转
            T = np.array([
                [np.cos(theta), -np.sin(theta), 0, 0],
                [np.sin(theta),  np.cos(theta), 0, 0],
                [0,              0,             1, length],
                [0,              0,             0, 1]
            ])
        else:
            # 弯曲段变换矩阵 (常曲率模型) - 向前弯曲
            phi = kappa * length  # 弯曲角度 (rad)
            radius = 1.0 / kappa  # 曲率半径 (mm)

            # 在xz平面内向前弯曲，形成平滑的弧形
            # 弯曲中心在原点左侧，管子向前弯曲
            x_bend = radius * np.sin(phi)  # 水平偏移
            z_bend = length - radius * (1 - np.cos(phi))  # 向前的轴向位移

            # 末端方向向量（切线方向）
            cos_phi = np.cos(phi)
            sin_phi = np.sin(phi)

            # 变换矩阵包含位置和方向
            T = np.array([
                [cos_phi * np.cos(theta) - sin_phi * np.sin(theta),
                 -cos_phi * np.sin(theta) - sin_phi * np.cos(theta), 0, x_bend * np.cos(theta)],
                [cos_phi * np.sin(theta) + sin_phi * np.cos(theta),
                 cos_phi * np.cos(theta) - sin_phi * np.sin(theta), 0, x_bend * np.sin(theta)],
                [0, 0, 1, z_bend],
                [0, 0, 0, 1]
            ])

        return T
    
    def forward_kinematics(self) -> Tuple[np.ndarray, List[np.ndarray], dict]:
        """
        计算前向运动学 (正确的同心管机器人结构)
        Calculate forward kinematics (correct concentric tube robot structure)

        正确的结构理解:
        1. 三根管子嵌套: 外管 → 中管 → 内管
        2. 只有内管有曲率 (预弯曲的柔性材料)
        3. 中管从外管伸出，内管从中管伸出
        4. 通过旋转内管改变弯曲方向来控制末端位置

        Returns:
        --------
        tip_pose : np.ndarray
            末端位姿 4x4矩阵
        segment_poses : List[np.ndarray]
            各段末端位姿列表
        tube_info : dict
            各管的详细信息
        """
        # 获取各管的伸出长度
        ext_outer = self.current_extensions['outer']
        ext_middle = self.current_extensions['middle']
        ext_inner = self.current_extensions['inner']

        # 获取各管的旋转角度
        rot_outer = self.current_rotations['outer']
        rot_middle = self.current_rotations['middle']
        rot_inner = self.current_rotations['inner']

        segment_poses = []
        tube_info = {}
        T_total = np.eye(4)

        # 第一段: 外管伸出部分 (直管，曲率=0)
        if ext_outer > 0:
            T_outer = self.transformation_matrix(
                kappa=0.0,  # 外管是直管
                length=ext_outer,
                theta=rot_outer
            )
            T_total = T_total @ T_outer
            segment_poses.append(T_total.copy())
            tube_info['outer_segment'] = {
                'length': ext_outer,
                'curvature': 0.0,
                'rotation': rot_outer,
                'description': '外管段 (直管)'
            }

        # 第二段: 中管从外管伸出的部分 (直管，曲率=0)
        if ext_middle > ext_outer:
            middle_extension = ext_middle - ext_outer
            T_middle = self.transformation_matrix(
                kappa=0.0,  # 中管也是直管
                length=middle_extension,
                theta=rot_middle
            )
            T_total = T_total @ T_middle
            segment_poses.append(T_total.copy())
            tube_info['middle_segment'] = {
                'length': middle_extension,
                'curvature': 0.0,
                'rotation': rot_middle,
                'description': '中管伸出段 (直管)'
            }

        # 第三段: 内管从中管伸出的部分 (弯曲管，有曲率)
        if ext_inner > ext_middle:
            inner_extension = ext_inner - ext_middle
            T_inner = self.transformation_matrix(
                kappa=self.tube_params['inner']['curvature'],  # 只有内管有曲率
                length=inner_extension,
                theta=rot_inner  # 旋转内管改变弯曲方向
            )
            T_total = T_total @ T_inner
            segment_poses.append(T_total.copy())
            tube_info['inner_segment'] = {
                'length': inner_extension,
                'curvature': self.tube_params['inner']['curvature'],
                'rotation': rot_inner,
                'description': '内管伸出段 (弯曲管，柔性材料)'
            }

        return T_total, segment_poses, tube_info

    def get_tip_position(self) -> np.ndarray:
        """
        获取末端位置
        Get tip position

        Returns:
        --------
        position : np.ndarray
            末端位置 [x, y, z] (mm)
        """
        tip_pose, _, _ = self.forward_kinematics()
        return tip_pose[:3, 3]

    def get_tip_orientation(self) -> np.ndarray:
        """
        获取末端姿态
        Get tip orientation

        Returns:
        --------
        orientation : np.ndarray
            末端姿态旋转矩阵 3x3
        """
        tip_pose, _, _ = self.forward_kinematics()
        return tip_pose[:3, :3]

    def get_robot_shape(self, n_points: int = 100) -> dict:
        """
        获取机器人完整形状点 - 显示所有同心管，确保连续性
        Get complete robot shape points - showing all concentric tubes with continuity

        Parameters:
        -----------
        n_points : int
            每个管的点数

        Returns:
        --------
        tubes_shape : dict
            包含各个管的形状数据
            {'outer': (x, y, z), 'middle': (x, y, z), 'inner': (x, y, z)}
        """
        # 获取各管的伸出长度和旋转角度
        ext_outer = self.current_extensions['outer']
        ext_middle = self.current_extensions['middle']
        ext_inner = self.current_extensions['inner']

        rot_outer = self.current_rotations['outer']
        rot_middle = self.current_rotations['middle']
        rot_inner = self.current_rotations['inner']

        tubes_shape = {}

        # 计算连续的机器人形状，确保各段之间平滑连接
        T_current = np.eye(4)  # 当前变换矩阵

        # 外管形状 (直管，从基座开始)
        if ext_outer > 0:
            s_values = np.linspace(0, ext_outer, n_points)
            x_outer = []
            y_outer = []
            z_outer = []

            for s in s_values:
                # 外管是直管，沿z轴延伸
                T_seg = self.transformation_matrix(0.0, s, rot_outer)
                pos = T_seg[:3, 3]
                x_outer.append(pos[0])
                y_outer.append(pos[1])
                z_outer.append(pos[2])

            tubes_shape['outer'] = (np.array(x_outer), np.array(y_outer), np.array(z_outer))

            # 更新当前变换矩阵到外管末端
            T_current = self.transformation_matrix(0.0, ext_outer, rot_outer)

        # 中管形状 (直管，从外管末端开始，确保连续性)
        if ext_middle > ext_outer:
            middle_length = ext_middle - ext_outer
            s_values = np.linspace(0, middle_length, n_points)
            x_middle = []
            y_middle = []
            z_middle = []

            for s in s_values:
                # 中管段的局部变换
                T_middle_local = self.transformation_matrix(0.0, s, rot_middle)
                # 应用到当前位置
                T_total = T_current @ T_middle_local
                pos = T_total[:3, 3]
                x_middle.append(pos[0])
                y_middle.append(pos[1])
                z_middle.append(pos[2])

            tubes_shape['middle'] = (np.array(x_middle), np.array(y_middle), np.array(z_middle))

            # 更新当前变换矩阵到中管末端
            T_middle_total = self.transformation_matrix(0.0, middle_length, rot_middle)
            T_current = T_current @ T_middle_total

        # 内管形状 (弯曲管，从中管末端开始，确保连续性)
        if ext_inner > ext_middle:
            inner_length = ext_inner - ext_middle
            s_values = np.linspace(0, inner_length, n_points)
            x_inner = []
            y_inner = []
            z_inner = []

            kappa = self.tube_params['inner']['curvature']

            for s in s_values:
                # 内管段的局部变换（有曲率）
                T_inner_local = self.transformation_matrix(kappa, s, rot_inner)
                # 应用到当前位置
                T_total = T_current @ T_inner_local
                pos = T_total[:3, 3]
                x_inner.append(pos[0])
                y_inner.append(pos[1])
                z_inner.append(pos[2])

            tubes_shape['inner'] = (np.array(x_inner), np.array(y_inner), np.array(z_inner))

        return tubes_shape
    
    def jacobian_matrix(self, delta: float = 0.01) -> np.ndarray:
        """
        计算雅可比矩阵 (数值微分)
        Calculate Jacobian matrix (numerical differentiation)
        
        Parameters:
        -----------
        delta : float
            微分步长
            
        Returns:
        --------
        J : np.ndarray
            雅可比矩阵 (3x6) - [x,y,z] vs [ext_out, ext_mid, ext_in, rot_out, rot_mid, rot_in]
        """
        # 获取当前末端位置
        current_pos = self.get_tip_position()
        
        # 初始化雅可比矩阵
        J = np.zeros((3, 6))
        
        # 对各个自由度进行数值微分
        variables = [
            ('outer', 'extension'), ('middle', 'extension'), ('inner', 'extension'),
            ('outer', 'rotation'), ('middle', 'rotation'), ('inner', 'rotation')
        ]
        
        for i, (tube_name, var_type) in enumerate(variables):
            # 保存原始值
            if var_type == 'extension':
                original_val = self.current_extensions[tube_name]
                self.current_extensions[tube_name] += delta
            else:  # rotation
                original_val = self.current_rotations[tube_name]
                self.current_rotations[tube_name] += delta
            
            # 计算扰动后的位置
            perturbed_pos = self.get_tip_position()
            
            # 计算偏导数
            J[:, i] = (perturbed_pos - current_pos) / delta
            
            # 恢复原始值
            if var_type == 'extension':
                self.current_extensions[tube_name] = original_val
            else:
                self.current_rotations[tube_name] = original_val
        
        return J
    
    def workspace_analysis(self, n_samples: int = 1000) -> np.ndarray:
        """
        工作空间分析 (嵌套结构)
        Workspace analysis (nested structure)

        Parameters:
        -----------
        n_samples : int
            采样点数量

        Returns:
        --------
        workspace_points : np.ndarray
            工作空间点集 (n_samples, 3)
        """
        workspace_points = []

        # 保存当前配置
        original_ext = self.current_extensions.copy()
        original_rot = self.current_rotations.copy()

        for _ in range(n_samples):
            # 随机生成配置
            extensions = {
                'outer': np.random.uniform(0, self.tube_params['outer']['max_extension']),
                'middle': np.random.uniform(0, self.tube_params['middle']['max_extension']),
                'inner': np.random.uniform(0, self.tube_params['inner']['max_extension'])
            }

            rotations = {
                'outer': np.random.uniform(0, 2*np.pi),
                'middle': np.random.uniform(0, 2*np.pi),
                'inner': np.random.uniform(0, 2*np.pi)
            }

            # 设置配置并计算末端位置
            self.set_configuration(extensions, rotations)
            tip_pos = self.get_tip_position()
            workspace_points.append(tip_pos)

        # 恢复原始配置
        self.current_extensions = original_ext
        self.current_rotations = original_rot

        return np.array(workspace_points)
    
    def plot_robot_shape(self, ax=None, n_points: int = 50, view_type='2d'):
        """
        绘制机器人形状 (支持2D和3D可视化)
        Plot robot shape (supports 2D and 3D visualization)

        Parameters:
        -----------
        ax : matplotlib axis
            绘图轴
        n_points : int
            每段的绘制点数
        view_type : str
            视图类型: '2d', '3d', 'multi_view'
        """
        if view_type == '3d':
            return self.plot_robot_3d(ax, n_points)
        elif view_type == 'multi_view':
            return self.plot_robot_multi_view(n_points)
        else:
            return self.plot_robot_2d(ax, n_points)

    def plot_robot_2d(self, ax=None, n_points: int = 50):
        """
        绘制2D机器人形状 (XY平面投影)
        Plot 2D robot shape (XY plane projection)
        """
        if ax is None:
            _, ax = plt.subplots(figsize=(10, 8))

        # 获取所有管的形状
        tubes_shape = self.get_robot_shape(n_points)

        # 定义管子的颜色和样式
        tube_styles = {
            'outer': {'color': 'gray', 'linewidth': 6, 'alpha': 0.7, 'label': '外管 (Outer)'},
            'middle': {'color': 'blue', 'linewidth': 4, 'alpha': 0.8, 'label': '中管 (Middle)'},
            'inner': {'color': 'red', 'linewidth': 2, 'alpha': 0.9, 'label': '内管 (Inner)'}
        }

        # 绘制各个管子
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax.plot(x_points, y_points, '-',
                       color=style['color'],
                       linewidth=style['linewidth'],
                       alpha=style['alpha'],
                       label=style['label'])

        # 标记关键点
        ax.plot(0, 0, 'ko', markersize=10, label='基座 (Base)', zorder=10)  # 基座

        # 标记末端点
        if 'inner' in tubes_shape:
            x_tip, y_tip, _ = tubes_shape['inner']
            ax.plot(x_tip[-1], y_tip[-1], 'ro', markersize=8, label='末端 (Tip)', zorder=10)
        elif 'middle' in tubes_shape:
            x_tip, y_tip, _ = tubes_shape['middle']
            ax.plot(x_tip[-1], y_tip[-1], 'bo', markersize=8, label='末端 (Tip)', zorder=10)
        elif 'outer' in tubes_shape:
            x_tip, y_tip, _ = tubes_shape['outer']
            ax.plot(x_tip[-1], y_tip[-1], 'go', markersize=8, label='末端 (Tip)', zorder=10)

        # 显示各管的伸出状态
        info_text = []
        for tube_name in ['outer', 'middle', 'inner']:
            ext = self.current_extensions[tube_name]
            rot = self.current_rotations[tube_name]
            max_ext = self.tube_params[tube_name]['max_extension']
            info_text.append(f'{tube_name.capitalize()}: {ext:.1f}/{max_ext:.1f}mm, {np.degrees(rot):.1f}°')

        # 添加信息文本
        ax.text(0.02, 0.98, '\n'.join(info_text),
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=9)

        # 绘制各管的伸出范围指示
        self._draw_tube_indicators(ax)

        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_title('Concentric Tube Robot - XY View (Top View)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

        return ax

    def plot_robot_3d(self, ax=None, n_points: int = 50):
        """
        绘制3D机器人形状
        Plot 3D robot shape
        """
        from mpl_toolkits.mplot3d import Axes3D

        if ax is None:
            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

        # 获取所有管的形状
        tubes_shape = self.get_robot_shape(n_points)

        # 定义管子的颜色和样式
        tube_styles = {
            'outer': {'color': 'gray', 'linewidth': 8, 'alpha': 0.7, 'label': '外管 (Outer)'},
            'middle': {'color': 'blue', 'linewidth': 6, 'alpha': 0.8, 'label': '中管 (Middle)'},
            'inner': {'color': 'red', 'linewidth': 4, 'alpha': 0.9, 'label': '内管 (Inner)'}
        }

        # 绘制各个管子
        all_x, all_y, all_z = [], [], []
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax.plot(x_points, y_points, z_points, '-',
                       color=style['color'],
                       linewidth=style['linewidth'],
                       alpha=style['alpha'],
                       label=style['label'])

                # 收集所有点用于设置坐标轴范围
                all_x.extend(x_points)
                all_y.extend(y_points)
                all_z.extend(z_points)

        # 标记关键点
        ax.scatter([0], [0], [0], color='black', s=120, label='基座 (Base)', zorder=10)  # 基座

        # 标记末端点
        if 'inner' in tubes_shape:
            x_tip, y_tip, z_tip = tubes_shape['inner']
            ax.scatter([x_tip[-1]], [y_tip[-1]], [z_tip[-1]],
                      color='red', s=100, label='末端 (Tip)', zorder=10)
        elif 'middle' in tubes_shape:
            x_tip, y_tip, z_tip = tubes_shape['middle']
            ax.scatter([x_tip[-1]], [y_tip[-1]], [z_tip[-1]],
                      color='blue', s=100, label='末端 (Tip)', zorder=10)
        elif 'outer' in tubes_shape:
            x_tip, y_tip, z_tip = tubes_shape['outer']
            ax.scatter([x_tip[-1]], [y_tip[-1]], [z_tip[-1]],
                      color='gray', s=100, label='末端 (Tip)', zorder=10)

        # 绘制坐标轴
        ax.quiver(0, 0, 0, 20, 0, 0, color='red', alpha=0.6, arrow_length_ratio=0.1)
        ax.quiver(0, 0, 0, 0, 20, 0, color='green', alpha=0.6, arrow_length_ratio=0.1)
        ax.quiver(0, 0, 0, 0, 0, 20, color='blue', alpha=0.6, arrow_length_ratio=0.1)

        # 设置标签和标题
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        ax.set_title('Concentric Tube Robot - 3D View')
        ax.legend()

        # 设置相等的坐标轴比例
        if all_x and all_y and all_z:
            max_range = max(max(all_x) - min(all_x),
                           max(all_y) - min(all_y),
                           max(all_z) - min(all_z)) / 2.0
            mid_x = (max(all_x) + min(all_x)) * 0.5
            mid_y = (max(all_y) + min(all_y)) * 0.5
            mid_z = (max(all_z) + min(all_z)) * 0.5
            ax.set_xlim(mid_x - max_range, mid_x + max_range)
            ax.set_ylim(mid_y - max_range, mid_y + max_range)
            ax.set_zlim(mid_z - max_range, mid_z + max_range)

        return ax

    def plot_robot_multi_view(self, n_points: int = 50):
        """
        绘制多视角机器人形状 (俯视图、侧视图、3D图)
        Plot multi-view robot shape (top view, side view, 3D view)
        """
        # 获取所有管的形状
        tubes_shape = self.get_robot_shape(n_points)

        # 定义管子的颜色和样式
        tube_styles = {
            'outer': {'color': 'gray', 'linewidth': 6, 'alpha': 0.7, 'label': '外管'},
            'middle': {'color': 'blue', 'linewidth': 4, 'alpha': 0.8, 'label': '中管'},
            'inner': {'color': 'red', 'linewidth': 2, 'alpha': 0.9, 'label': '内管'}
        }

        # 创建多子图
        fig = plt.figure(figsize=(18, 12))

        # 1. 俯视图 (XY平面 - Top View)
        ax1 = plt.subplot(2, 3, 1)
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax1.plot(x_points, y_points, '-',
                        color=style['color'],
                        linewidth=style['linewidth'],
                        alpha=style['alpha'],
                        label=style['label'])

        ax1.plot(0, 0, 'ko', markersize=10, label='基座')
        # 标记末端
        if 'inner' in tubes_shape:
            x_tip, y_tip, _ = tubes_shape['inner']
            ax1.plot(x_tip[-1], y_tip[-1], 'ro', markersize=8, label='末端')

        ax1.set_xlabel('X (mm)')
        ax1.set_ylabel('Y (mm)')
        ax1.set_title('Top View (俯视图) - XY Plane')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        ax1.legend()

        # 2. 侧视图 (XZ平面 - Side View)
        ax2 = plt.subplot(2, 3, 2)
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax2.plot(x_points, z_points, '-',
                        color=style['color'],
                        linewidth=style['linewidth'],
                        alpha=style['alpha'],
                        label=style['label'])

        ax2.plot(0, 0, 'ko', markersize=10, label='基座')
        if 'inner' in tubes_shape:
            x_tip, _, z_tip = tubes_shape['inner']
            ax2.plot(x_tip[-1], z_tip[-1], 'ro', markersize=8, label='末端')

        ax2.set_xlabel('X (mm)')
        ax2.set_ylabel('Z (mm)')
        ax2.set_title('Side View (侧视图) - XZ Plane')
        ax2.grid(True, alpha=0.3)
        ax2.axis('equal')
        ax2.legend()

        # 3. 正视图 (YZ平面 - Front View)
        ax3 = plt.subplot(2, 3, 3)
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax3.plot(y_points, z_points, '-',
                        color=style['color'],
                        linewidth=style['linewidth'],
                        alpha=style['alpha'],
                        label=style['label'])

        ax3.plot(0, 0, 'ko', markersize=10, label='基座')
        if 'inner' in tubes_shape:
            _, y_tip, z_tip = tubes_shape['inner']
            ax3.plot(y_tip[-1], z_tip[-1], 'ro', markersize=8, label='末端')

        ax3.set_xlabel('Y (mm)')
        ax3.set_ylabel('Z (mm)')
        ax3.set_title('Front View (正视图) - YZ Plane')
        ax3.grid(True, alpha=0.3)
        ax3.axis('equal')
        ax3.legend()

        # 4. 3D视图
        ax4 = plt.subplot(2, 3, (4, 6), projection='3d')
        for tube_name in ['outer', 'middle', 'inner']:
            if tube_name in tubes_shape:
                x_points, y_points, z_points = tubes_shape[tube_name]
                style = tube_styles[tube_name]
                ax4.plot(x_points, y_points, z_points, '-',
                        color=style['color'],
                        linewidth=style['linewidth'],
                        alpha=style['alpha'],
                        label=style['label'])

        ax4.scatter([0], [0], [0], color='black', s=120, label='基座')
        if 'inner' in tubes_shape:
            x_tip, y_tip, z_tip = tubes_shape['inner']
            ax4.scatter([x_tip[-1]], [y_tip[-1]], [z_tip[-1]],
                       color='red', s=100, label='末端')

        # 绘制坐标轴
        ax4.quiver(0, 0, 0, 20, 0, 0, color='red', alpha=0.6, arrow_length_ratio=0.1)
        ax4.quiver(0, 0, 0, 0, 20, 0, color='green', alpha=0.6, arrow_length_ratio=0.1)
        ax4.quiver(0, 0, 0, 0, 0, 20, color='blue', alpha=0.6, arrow_length_ratio=0.1)

        ax4.set_xlabel('X (mm)')
        ax4.set_ylabel('Y (mm)')
        ax4.set_zlabel('Z (mm)')
        ax4.set_title('3D View (三维视图)')
        ax4.legend()

        # 设置相等的坐标轴比例
        max_range = max(max(x_points) - min(x_points),
                       max(y_points) - min(y_points),
                       max(z_points) - min(z_points)) / 2.0
        mid_x = (max(x_points) + min(x_points)) * 0.5
        mid_y = (max(y_points) + min(y_points)) * 0.5
        mid_z = (max(z_points) + min(z_points)) * 0.5
        ax4.set_xlim(mid_x - max_range, mid_x + max_range)
        ax4.set_ylim(mid_y - max_range, mid_y + max_range)
        ax4.set_zlim(mid_z - max_range, mid_z + max_range)

        # 添加配置信息
        info_text = []
        for tube_name in ['outer', 'middle', 'inner']:
            ext = self.current_extensions[tube_name]
            rot = self.current_rotations[tube_name]
            max_ext = self.tube_params[tube_name]['max_extension']
            info_text.append(f'{tube_name.capitalize()}: {ext:.1f}/{max_ext:.1f}mm, {np.degrees(rot):.1f}°')

        fig.suptitle('Concentric Tube Robot - Multi-View Analysis\n' + ' | '.join(info_text),
                    fontsize=12, y=0.95)

        plt.tight_layout()
        return fig

    def _draw_tube_indicators(self, ax):
        """
        绘制各管的伸出范围指示
        Draw tube extension range indicators
        """
        colors = ['red', 'green', 'blue']
        alphas = [0.3, 0.4, 0.5]
        labels = ['Outer Range', 'Middle Range', 'Inner Range']

        for i, tube_name in enumerate(['outer', 'middle', 'inner']):
            max_ext = self.tube_params[tube_name]['max_extension']
            current_ext = self.current_extensions[tube_name]

            if max_ext > 0:
                # 绘制最大伸出范围的圆
                circle_max = plt.Circle((0, 0), max_ext,
                                      color=colors[i], alpha=alphas[i]/2,
                                      fill=False, linestyle='--', linewidth=1)
                ax.add_patch(circle_max)

                # 绘制当前伸出位置的圆
                if current_ext > 0:
                    circle_current = plt.Circle((0, 0), current_ext,
                                              color=colors[i], alpha=alphas[i],
                                              fill=False, linewidth=2)
                    ax.add_patch(circle_current)
