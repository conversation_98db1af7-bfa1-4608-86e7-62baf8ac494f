'''
    Author: <PERSON><PERSON><PERSON> Can KARGIN
    Python Version: 3.9.7
    
This forward_velocity_kinematics.py file contains all necessary kinematics functions for the project
'''

import numpy as np

# %% Function three_section_planar_robot
def three_section_planar_robot(kappa1, kappa2, kappa3, l): # TODO -> Add if else to figure out when any kappa = 0
    '''
    * Homogeneous transformation matrix 
    * Mapping from configuration parameters to task space for the tip of the continuum robot
    
    Parameters
    ----------
    kappa1 : float
        curvature value for section 1.
    kappa2 : float
        curvature value for section 2.
    kappa3 : float
        curvature value for section 3.
    l : list
        trunk length contains all sections

    Returns
    -------
    T: numpy array
        Transformation matrices containing orientation and position

    '''
    
    c_ks = np.cos((kappa1*l[0])+(kappa2*l[1])+(kappa3*l[2]));
    
    s_ks = np.sin((kappa1*l[0])+(kappa2*l[1])+(kappa3*l[2]));
    
    A_14 = ((np.cos(kappa1*l[0])-1)/kappa1) + ((np.cos((kappa1*l[0])+(kappa2*l[1]))-np.cos(kappa1*l[0]))/kappa2) + ((np.cos((kappa1*l[0])+(kappa2*l[1])+(kappa3*l[2]))-np.cos((kappa1*l[0])+(kappa2*l[1])))/kappa3)
    
    A_24 = ((np.sin(kappa1*l[0]))/kappa1) + ((np.sin((kappa1*l[0])+(kappa2*l[1]))-np.sin(kappa1*l[0]))/kappa2) + ((np.sin((kappa1*l[0])+(kappa2*l[1])+(kappa3*l[2]))-np.sin((kappa1*l[0])+(kappa2*l[1])))/kappa3)

    T = np.array([c_ks,s_ks,0,0,-s_ks,c_ks,0,0,0,0,1,0,A_14,A_24,0,1]);
        
    T = np.reshape(T,(4,4),order='F');
    return T

# %% Function three_section_planar_robot
def jacobian_matrix(delta_kappa, kappa1, kappa2, kappa3, l): # TODO -> figure out singularity
    '''
    * Calculation of jacobian matrix by numerical differentation    

    Parameters
    ----------
    delta_kappa : float
        parameter for numerical differentation. Commonly 0.1
    kappa1 : float
        curvature value for section 1.
    kappa2 : float
        curvature value for section 2.
    kappa3 : float
        curvature value for section 3.
    l : list
        trunk length contains all sections

    Returns
    -------
    J : Numpy array with the shape of (2,3)
        Jacobian Matrix

    '''
    
    J11 = (three_section_planar_robot(kappa1+delta_kappa,kappa2,kappa3,l)[0,3] - three_section_planar_robot(kappa1-delta_kappa,kappa2,kappa3,l))[0,3] / (2*delta_kappa);
    J12 = (three_section_planar_robot(kappa1,kappa2+delta_kappa,kappa3,l)[0,3] - three_section_planar_robot(kappa1,kappa2-delta_kappa,kappa3,l))[0,3] / (2*delta_kappa);
    J13 = (three_section_planar_robot(kappa1,kappa2,kappa3+delta_kappa,l)[0,3] - three_section_planar_robot(kappa1,kappa2,kappa3-delta_kappa,l))[0,3] / (2*delta_kappa);
    J21 = (three_section_planar_robot(kappa1+delta_kappa,kappa2,kappa3,l)[1,3] - three_section_planar_robot(kappa1-delta_kappa,kappa2,kappa3,l))[1,3] / (2*delta_kappa);
    J22 = (three_section_planar_robot(kappa1,kappa2+delta_kappa,kappa3,l)[1,3] - three_section_planar_robot(kappa1,kappa2-delta_kappa,kappa3,l))[1,3] / (2*delta_kappa);
    J23 = (three_section_planar_robot(kappa1,kappa2,kappa3+delta_kappa,l)[1,3] - three_section_planar_robot(kappa1,kappa2,kappa3-delta_kappa,l))[1,3] / (2*delta_kappa);
    
    J = np.array([J11,J12,J13,J21,J22,J23]);
    J = np.reshape(J,(2,3))
    
    return J

# %% Function three_section_planar_robot

# Planar Robot Kinematics Functions
def trans_mat_cc(kappa, l):
    '''
    *  Homogeneous transformation matrix
    *  Mapping from configuration parameters to task space
    * tip frame is aligned so that the x-axis points toward the center of the circle

    Parameters
    ----------
    kappa : list
        curvature value for all sections
    l : list
        trunk length contains all sections

    Returns
    -------
    T: numpy array
        Transformation matrices containing orientation and position

    '''

    # num = sect_points: points per section
    si=np.linspace(0,l, num = 50);
    T= np.zeros((len(si),16));
    
    for i in range(len(si)):
        s=si[i];
        c_ks=np.cos(kappa*s);
        s_ks=np.sin(kappa*s);
        if kappa==0:
            T[i,:] = np.array([c_ks,s_ks,0,0,-s_ks,c_ks,0,0,0,0,1,0,0,s,0,1]);  
        else:
            T[i,:] = np.array([c_ks,s_ks,0,0,-s_ks,c_ks,0,0,0,0,1,0,(c_ks-1)/kappa,s_ks/kappa,0,1]);

    return T

# %% Function three_section_planar_robot
def coupletransformations(T,T_tip):
    '''
    * The forward kinematics for an n section manipulator can then be generated by the product of 
    n matrices. The forward kinematics for our elephant trunk robot with its n sections can be calculated with this function
    * Find orientation and position of distal section (Multiply T of current section with T at tip of previous section)
    

    Parameters
    ----------
    T : np array
        Transformation matrices of current section.
    T_tip : np array
        Transformation at tip of previous section

    Returns
    -------
    Tc : np array
        coupled Transformation matrix

    '''

    Tc=np.zeros((len(T[:,0]),len(T[0,:])));
    for k in range(len(T[:,0])):
        #Tc[k,:].reshape(-1,1)
        p = np.matmul(T_tip,(np.reshape(T[k,:],(4,4),order='F')))
        Tc[k,:] = np.reshape(p,(16,),order='F');
    return Tc