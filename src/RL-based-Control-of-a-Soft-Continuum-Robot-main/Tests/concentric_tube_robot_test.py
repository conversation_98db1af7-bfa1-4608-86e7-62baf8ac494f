'''
    三管同心管机器人测试脚本
    Three-tube Concentric Tube Robot Test Script
    
    测试功能:
    1. 前向运动学计算
    2. 雅可比矩阵计算
    3. 工作空间分析
    4. 机器人形状可视化
'''

import sys
import os
sys.path.append('../')

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from kinematics.concentric_tube_robot import ConcentricTubeRobot

def test_forward_kinematics():
    """测试前向运动学 (嵌套结构)"""
    print("=" * 50)
    print("测试前向运动学 - 嵌套结构 (Forward Kinematics Test - Nested Structure)")
    print("=" * 50)

    # 创建机器人实例
    robot = ConcentricTubeRobot()

    # 设置测试配置 - 展示嵌套结构特性
    test_configs = [
        {
            'extensions': {'outer': 10.0, 'middle': 20.0, 'inner': 30.0},
            'rotations': {'outer': 0.0, 'middle': 0.0, 'inner': 0.0},
            'description': '递增伸出 - 展示嵌套结构'
        },
        {
            'extensions': {'outer': 30.0, 'middle': 20.0, 'inner': 10.0},
            'rotations': {'outer': 0.0, 'middle': 0.0, 'inner': 0.0},
            'description': '外管最长 - 直管主导'
        },
        {
            'extensions': {'outer': 10.0, 'middle': 30.0, 'inner': 100.0},
            'rotations': {'outer': 0.0, 'middle': 0.0, 'inner': 0.0},
            'description': '内管最长 - 弯曲主导'
        },
        {
            'extensions': {'outer': 33.0, 'middle': 73.0, 'inner': 175.0},
            'rotations': {'outer': 0.0, 'middle': np.pi/4, 'inner': np.pi/2},
            'description': '最大伸出 + 旋转'
        },
        {
            'extensions': {'outer': 15.0, 'middle': 40.0, 'inner': 80.0},
            'rotations': {'outer': np.pi/6, 'middle': np.pi/3, 'inner': np.pi/2},
            'description': '中等伸出 + 多角度旋转'
        }
    ]

    for i, config in enumerate(test_configs):
        print(f"\n配置 {i+1}: {config['description']}")
        print(f"伸出长度: {config['extensions']}")
        print(f"旋转角度: {config['rotations']}")

        # 设置配置
        robot.set_configuration(config['extensions'], config['rotations'])

        # 计算前向运动学
        tip_pose, segment_poses, tube_info = robot.forward_kinematics()
        tip_position = robot.get_tip_position()

        print(f"末端位置: [{tip_position[0]:.2f}, {tip_position[1]:.2f}, {tip_position[2]:.2f}] mm")
        print(f"段数量: {len(segment_poses)}")
        print(f"段信息: {list(tube_info.keys())}")

        # 显示各段详细信息
        for segment_name, info in tube_info.items():
            print(f"  {segment_name}: 长度={info['length']:.1f}mm, 曲率={info['curvature']:.6f}, 旋转={np.degrees(info['rotation']):.1f}°")

def test_jacobian():
    """测试雅可比矩阵计算"""
    print("\n" + "=" * 50)
    print("测试雅可比矩阵 (Jacobian Matrix Test)")
    print("=" * 50)
    
    robot = ConcentricTubeRobot()
    
    # 设置测试配置
    extensions = {'outer': 20.0, 'middle': 50.0, 'inner': 100.0}
    rotations = {'outer': 0.0, 'middle': np.pi/4, 'inner': np.pi/2}
    
    robot.set_configuration(extensions, rotations)
    
    # 计算雅可比矩阵
    J = robot.jacobian_matrix()
    
    print(f"雅可比矩阵形状: {J.shape}")
    print("雅可比矩阵:")
    print("列顺序: [外管伸出, 中管伸出, 内管伸出, 外管旋转, 中管旋转, 内管旋转]")
    print(J)
    
    # 分析奇异值
    U, s, Vt = np.linalg.svd(J)
    print(f"\n奇异值: {s}")
    print(f"条件数: {np.max(s)/np.min(s):.2f}")

def test_workspace_analysis():
    """测试工作空间分析"""
    print("\n" + "=" * 50)
    print("工作空间分析 (Workspace Analysis)")
    print("=" * 50)
    
    robot = ConcentricTubeRobot()
    
    # 生成工作空间点
    print("生成工作空间点...")
    workspace_points = robot.workspace_analysis(n_samples=500)
    
    print(f"工作空间点数: {len(workspace_points)}")
    print(f"X范围: [{np.min(workspace_points[:, 0]):.2f}, {np.max(workspace_points[:, 0]):.2f}] mm")
    print(f"Y范围: [{np.min(workspace_points[:, 1]):.2f}, {np.max(workspace_points[:, 1]):.2f}] mm")
    print(f"Z范围: [{np.min(workspace_points[:, 2]):.2f}, {np.max(workspace_points[:, 2]):.2f}] mm")
    
    # 绘制工作空间
    fig = plt.figure(figsize=(15, 5))
    
    # 2D视图 (XY平面)
    ax1 = fig.add_subplot(131)
    ax1.scatter(workspace_points[:, 0], workspace_points[:, 1], 
               alpha=0.6, s=1, c='blue')
    ax1.set_xlabel('X (mm)')
    ax1.set_ylabel('Y (mm)')
    ax1.set_title('工作空间 - XY平面')
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')
    
    # 2D视图 (XZ平面)
    ax2 = fig.add_subplot(132)
    ax2.scatter(workspace_points[:, 0], workspace_points[:, 2], 
               alpha=0.6, s=1, c='red')
    ax2.set_xlabel('X (mm)')
    ax2.set_ylabel('Z (mm)')
    ax2.set_title('工作空间 - XZ平面')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 3D视图
    ax3 = fig.add_subplot(133, projection='3d')
    ax3.scatter(workspace_points[:, 0], workspace_points[:, 1], workspace_points[:, 2],
               alpha=0.6, s=1, c='green')
    ax3.set_xlabel('X (mm)')
    ax3.set_ylabel('Y (mm)')
    ax3.set_zlabel('Z (mm)')
    ax3.set_title('工作空间 - 3D视图')
    
    plt.tight_layout()
    plt.savefig('concentric_tube_workspace.png', dpi=300, bbox_inches='tight')
    print("工作空间图已保存为: concentric_tube_workspace.png")

def test_robot_visualization():
    """测试机器人形状可视化"""
    print("\n" + "=" * 50)
    print("机器人形状可视化 (Robot Shape Visualization)")
    print("=" * 50)
    
    robot = ConcentricTubeRobot()
    
    # 创建多个配置进行可视化
    configs = [
        {
            'extensions': {'outer': 33.0, 'middle': 73.0, 'inner': 175.0},
            'rotations': {'outer': 0.0, 'middle': 0.0, 'inner': 0.0},
            'title': '配置1: 最大伸出, 无旋转'
        },
        {
            'extensions': {'outer': 25.0, 'middle': 60.0, 'inner': 150.0},
            'rotations': {'outer': 0.0, 'middle': np.pi/3, 'inner': 2*np.pi/3},
            'title': '配置2: 部分伸出 + 旋转'
        },
        {
            'extensions': {'outer': 20.0, 'middle': 45.0, 'inner': 120.0},
            'rotations': {'outer': np.pi/4, 'middle': np.pi/2, 'inner': 3*np.pi/4},
            'title': '配置3: 复杂旋转配置'
        },
        {
            'extensions': {'outer': 10.0, 'middle': 30.0, 'inner': 80.0},
            'rotations': {'outer': np.pi/6, 'middle': -np.pi/4, 'inner': np.pi},
            'title': '配置4: 小伸出 + 反向旋转'
        }
    ]
    
    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i, config in enumerate(configs):
        robot.set_configuration(config['extensions'], config['rotations'])
        
        ax = robot.plot_robot_shape(axes[i])
        ax.set_title(config['title'])
        
        # 显示末端位置信息
        tip_pos = robot.get_tip_position()
        ax.text(0.02, 0.98, f'末端位置:\nX: {tip_pos[0]:.1f}mm\nY: {tip_pos[1]:.1f}mm', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('concentric_tube_configurations.png', dpi=300, bbox_inches='tight')
    print("机器人配置图已保存为: concentric_tube_configurations.png")

def performance_analysis():
    """性能分析"""
    print("\n" + "=" * 50)
    print("性能分析 (Performance Analysis)")
    print("=" * 50)
    
    robot = ConcentricTubeRobot()
    
    # 测试计算速度
    import time
    
    # 前向运动学速度测试
    start_time = time.time()
    n_tests = 1000
    
    for _ in range(n_tests):
        extensions = {
            'outer': np.random.uniform(0, 33),
            'middle': np.random.uniform(0, 73),
            'inner': np.random.uniform(0, 175)
        }
        rotations = {
            'outer': np.random.uniform(0, 2*np.pi),
            'middle': np.random.uniform(0, 2*np.pi),
            'inner': np.random.uniform(0, 2*np.pi)
        }
        robot.set_configuration(extensions, rotations)
        _ = robot.get_tip_position()
    
    fk_time = (time.time() - start_time) / n_tests
    print(f"前向运动学平均计算时间: {fk_time*1000:.3f} ms")
    
    # 雅可比矩阵速度测试
    start_time = time.time()
    n_jacobian_tests = 100
    
    for _ in range(n_jacobian_tests):
        _ = robot.jacobian_matrix()
    
    jacobian_time = (time.time() - start_time) / n_jacobian_tests
    print(f"雅可比矩阵平均计算时间: {jacobian_time*1000:.3f} ms")

def main():
    """主函数"""
    print("三管同心管机器人测试程序")
    print("Three-tube Concentric Tube Robot Test Program")
    print("机器人参数:")
    print("- 外管: 长度55mm, 伸缩33mm, 曲率0")
    print("- 中管: 长度105mm, 伸缩73mm, 曲率0")
    print("- 内管: 长度215mm, 伸缩175mm, 曲率1/143mm")
    
    # 运行所有测试
    test_forward_kinematics()
    test_jacobian()
    test_workspace_analysis()
    test_robot_visualization()
    performance_analysis()
    
    print("\n" + "=" * 50)
    print("所有测试完成!")
    print("生成的文件:")
    print("- concentric_tube_workspace.png: 工作空间分析图")
    print("- concentric_tube_configurations.png: 机器人配置图")
    print("=" * 50)
    
    # 显示图形
    plt.show()

if __name__ == "__main__":
    main()
