'''
    三管同心管机器人强化学习环境
    Three-tube Concentric Tube Robot Reinforcement Learning Environment
    
    Author: AI Assistant
    Python Version: 3.9.7
'''

import sys
import os
sys.path.append('../')

import gym
import numpy as np
import math
from gym import spaces
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Any

from kinematics.concentric_tube_robot import ConcentricTubeRobot

class ConcentricTubeEnv(gym.Env):
    """
    三管同心管机器人强化学习环境
    Three-tube Concentric Tube Robot RL Environment
    
    动作空间: 6维连续动作
    - [外管伸出速度, 中管伸出速度, 内管伸出速度, 外管旋转速度, 中管旋转速度, 内管旋转速度]
    
    观测空间: 9维状态
    - [当前末端位置(3), 目标位置(3), 当前配置(6)]
    """
    
    def __init__(self, 
                 max_extension_velocity: float = 5.0,  # mm/step
                 max_rotation_velocity: float = 0.1,   # rad/step
                 position_tolerance: float = 2.0,      # mm
                 max_steps: int = 500):
        """
        初始化环境
        
        Parameters:
        -----------
        max_extension_velocity : float
            最大伸出速度 (mm/step)
        max_rotation_velocity : float
            最大旋转速度 (rad/step)
        position_tolerance : float
            位置容差 (mm)
        max_steps : int
            最大步数
        """
        super(ConcentricTubeEnv, self).__init__()
        
        # 创建机器人实例
        self.robot = ConcentricTubeRobot()
        
        # 环境参数
        self.max_extension_velocity = max_extension_velocity
        self.max_rotation_velocity = max_rotation_velocity
        self.position_tolerance = position_tolerance
        self.max_steps = max_steps
        
        # 动作空间: [ext_vel_out, ext_vel_mid, ext_vel_in, rot_vel_out, rot_vel_mid, rot_vel_in]
        self.action_space = spaces.Box(
            low=-1.0, 
            high=1.0, 
            shape=(6,), 
            dtype=np.float32
        )
        
        # 观测空间: [current_pos(3), target_pos(3), current_config(6)]
        # 位置范围基于工作空间分析
        pos_low = np.array([-300.0, -300.0, -50.0])  # mm
        pos_high = np.array([300.0, 300.0, 50.0])    # mm
        
        # 配置空间
        ext_low = np.array([0.0, 0.0, 0.0])
        ext_high = np.array([33.0, 73.0, 175.0])  # 最大伸出长度
        rot_low = np.array([0.0, 0.0, 0.0])
        rot_high = np.array([2*np.pi, 2*np.pi, 2*np.pi])
        
        obs_low = np.concatenate([pos_low, pos_low, ext_low, rot_low])
        obs_high = np.concatenate([pos_high, pos_high, ext_high, rot_high])
        
        self.observation_space = spaces.Box(
            low=obs_low,
            high=obs_high,
            dtype=np.float32
        )
        
        # 状态变量
        self.current_step = 0
        self.target_position = np.zeros(3)
        self.initial_position = np.zeros(3)
        self.trajectory = []
        
        # 重置环境
        self.reset()
    
    def reset(self) -> np.ndarray:
        """
        重置环境
        
        Returns:
        --------
        observation : np.ndarray
            初始观测
        """
        self.current_step = 0
        self.trajectory = []
        
        # 随机初始配置 (更合理的范围)
        initial_extensions = {
            'outer': np.random.uniform(5, 25),
            'middle': np.random.uniform(10, 50),
            'inner': np.random.uniform(20, 120)
        }
        
        initial_rotations = {
            'outer': np.random.uniform(0, 2*np.pi),
            'middle': np.random.uniform(0, 2*np.pi),
            'inner': np.random.uniform(0, 2*np.pi)
        }
        
        self.robot.set_configuration(initial_extensions, initial_rotations)
        self.initial_position = self.robot.get_tip_position()
        
        # 随机目标位置 (在工作空间内)
        self._generate_random_target()
        
        return self._get_observation()
    
    def _generate_random_target(self):
        """生成随机目标位置 (修复版本)"""
        # 生成相对容易达到的目标位置
        target_extensions = {
            'outer': np.random.uniform(8, 30),
            'middle': np.random.uniform(15, 60),
            'inner': np.random.uniform(30, 150)
        }
        
        target_rotations = {
            'outer': np.random.uniform(0, 2*np.pi),
            'middle': np.random.uniform(0, 2*np.pi),
            'inner': np.random.uniform(0, 2*np.pi)
        }
        
        # 临时设置配置以获取目标位置
        original_ext = self.robot.current_extensions.copy()
        original_rot = self.robot.current_rotations.copy()
        
        self.robot.set_configuration(target_extensions, target_rotations)
        self.target_position = self.robot.get_tip_position()
        
        # 恢复原始配置
        self.robot.set_configuration(original_ext, original_rot)
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """
        执行一步动作
        
        Parameters:
        -----------
        action : np.ndarray
            动作向量 [ext_vel_out, ext_vel_mid, ext_vel_in, rot_vel_out, rot_vel_mid, rot_vel_in]
            
        Returns:
        --------
        observation : np.ndarray
            新的观测
        reward : float
            奖励
        done : bool
            是否结束
        info : dict
            额外信息
        """
        self.current_step += 1
        
        # 限制动作范围
        action = np.clip(action, -1.0, 1.0)
        
        # 转换为实际速度
        ext_velocities = action[:3] * self.max_extension_velocity
        rot_velocities = action[3:] * self.max_rotation_velocity
        
        # 获取当前配置
        current_ext = self.robot.current_extensions
        current_rot = self.robot.current_rotations
        
        # 更新配置
        new_extensions = {
            'outer': np.clip(current_ext['outer'] + ext_velocities[0], 0, 33.0),
            'middle': np.clip(current_ext['middle'] + ext_velocities[1], 0, 73.0),
            'inner': np.clip(current_ext['inner'] + ext_velocities[2], 0, 175.0)
        }
        
        new_rotations = {
            'outer': (current_rot['outer'] + rot_velocities[0]) % (2*np.pi),
            'middle': (current_rot['middle'] + rot_velocities[1]) % (2*np.pi),
            'inner': (current_rot['inner'] + rot_velocities[2]) % (2*np.pi)
        }
        
        # 设置新配置
        self.robot.set_configuration(new_extensions, new_rotations)
        
        # 获取新位置
        current_position = self.robot.get_tip_position()
        self.trajectory.append(current_position.copy())
        
        # 计算奖励
        reward = self._calculate_reward(current_position, action)
        
        # 检查是否完成
        distance_to_target = np.linalg.norm(current_position - self.target_position)
        done = (distance_to_target < self.position_tolerance) or (self.current_step >= self.max_steps)
        
        # 额外信息
        info = {
            'distance_to_target': distance_to_target,
            'current_position': current_position,
            'target_position': self.target_position,
            'step': self.current_step,
            'success': distance_to_target < self.position_tolerance
        }
        
        return self._get_observation(), reward, done, info
    
    def _calculate_reward(self, current_position: np.ndarray, action: np.ndarray) -> float:
        """
        计算奖励函数 (修复版本)

        Parameters:
        -----------
        current_position : np.ndarray
            当前位置
        action : np.ndarray
            执行的动作

        Returns:
        --------
        reward : float
            奖励值
        """
        # 计算到目标的距离
        distance = np.linalg.norm(current_position - self.target_position)

        # 距离奖励 (归一化，避免过大的负奖励)
        max_distance = 300.0  # 假设最大可能距离
        distance_reward = 10.0 * (1.0 - distance / max_distance)  # 0到10之间

        # 到达目标奖励
        if distance < self.position_tolerance:
            success_reward = 100.0
        else:
            success_reward = 0.0

        # 接近目标的额外奖励 (分层奖励)
        proximity_reward = 0.0
        if distance < 10.0:  # 10mm内
            proximity_reward = 20.0
        elif distance < 20.0:  # 20mm内
            proximity_reward = 10.0
        elif distance < 50.0:  # 50mm内
            proximity_reward = 5.0

        # 动作平滑性惩罚 (减小惩罚)
        action_penalty = -0.01 * np.sum(np.abs(action))

        # 轻微的步数惩罚
        step_penalty = -0.01

        # 总奖励
        total_reward = distance_reward + success_reward + proximity_reward + action_penalty + step_penalty

        return total_reward
    
    def _get_observation(self) -> np.ndarray:
        """
        获取当前观测
        
        Returns:
        --------
        observation : np.ndarray
            观测向量
        """
        current_position = self.robot.get_tip_position()
        
        # 当前配置
        current_ext = self.robot.current_extensions
        current_rot = self.robot.current_rotations
        
        config_vector = np.array([
            current_ext['outer'], current_ext['middle'], current_ext['inner'],
            current_rot['outer'], current_rot['middle'], current_rot['inner']
        ])
        
        # 组合观测
        observation = np.concatenate([
            current_position,      # 当前位置 (3)
            self.target_position,  # 目标位置 (3)
            config_vector          # 当前配置 (6)
        ])
        
        return observation.astype(np.float32)
    
    def render(self, mode='human'):
        """
        渲染环境
        
        Parameters:
        -----------
        mode : str
            渲染模式
        """
        if mode == 'human':
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 绘制机器人形状
            self.robot.plot_robot_shape(ax1)
            
            # 标记目标位置
            ax1.plot(self.target_position[0], self.target_position[1], 
                    'r*', markersize=15, label='Target')
            ax1.legend()
            ax1.set_title(f'Step {self.current_step}')
            
            # 绘制轨迹
            if len(self.trajectory) > 1:
                trajectory = np.array(self.trajectory)
                ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', alpha=0.7, label='Trajectory')
                ax2.plot(self.initial_position[0], self.initial_position[1], 
                        'go', markersize=8, label='Start')
                ax2.plot(self.target_position[0], self.target_position[1], 
                        'r*', markersize=15, label='Target')
                ax2.plot(trajectory[-1, 0], trajectory[-1, 1], 
                        'bo', markersize=8, label='Current')
            
            ax2.set_xlabel('X (mm)')
            ax2.set_ylabel('Y (mm)')
            ax2.set_title('Trajectory')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.axis('equal')
            
            plt.tight_layout()
            plt.pause(0.01)
    
    def close(self):
        """关闭环境"""
        plt.close('all')
    
    def get_robot_info(self) -> Dict[str, Any]:
        """
        获取机器人信息
        
        Returns:
        --------
        info : dict
            机器人信息
        """
        return {
            'tube_params': self.robot.tube_params,
            'current_extensions': self.robot.current_extensions,
            'current_rotations': self.robot.current_rotations,
            'tip_position': self.robot.get_tip_position(),
            'target_position': self.target_position
        }
