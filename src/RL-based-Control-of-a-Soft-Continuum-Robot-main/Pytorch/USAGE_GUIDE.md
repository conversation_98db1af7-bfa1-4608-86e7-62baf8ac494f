# 🚀 DDPG软体机器人控制系统使用指南

## 📋 系统概述

这个DDPG (Deep Deterministic Policy Gradient) 系统用于训练软体连续机器人的强化学习控制器。

## 🔧 快速开始

### 1. 环境准备
确保已安装所有依赖：
```bash
pip install -r ../requirements.txt
# 或使用conda环境
conda env create -f ../environment.yml
conda activate rlcontrol
```

### 2. 训练新模型

#### 步骤1：配置训练参数
编辑 `config.yaml` 文件：
```yaml
goal_type: 'fixed_goal'  # 固定目标或随机目标
reward:
  function: 'step_minus_weighted_euclidean'  # 奖励函数
  file: 'reward_step_minus_weighted_euclidean'  # 保存目录
```

#### 步骤2：启用训练模式
在 `ddpg.py` 中设置：
```python
TRAIN = True  # 启用训练
```

#### 步骤3：运行训练
```bash
python ddpg.py
```

### 3. 使用预训练模型

#### 步骤1：禁用训练模式
```python
TRAIN = False  # 仅加载模型，不训练
```

#### 步骤2：运行测试
```bash
python ddpg.py
```

## ⚙️ 配置选项

### 训练参数
在 `ddpg()` 函数中调整：
- `n_episodes=300`: 训练回合数
- `max_t=750`: 每回合最大步数  
- `print_every=25`: 打印频率

### 奖励函数选项
1. **step_minus_weighted_euclidean**: 加权欧几里得距离 (推荐)
2. **step_minus_euclidean_square**: 欧几里得距离平方
3. **step_error_comparison**: 误差比较
4. **step_distance_based**: 基于距离变化

### 目标类型
- **fixed_goal**: 固定目标位置
- **random_goal**: 随机目标位置

## 📊 训练监控

训练过程中会显示：
```
Episode 25	Average Score: -2.34
Initial Position is [-0.1234, 0.5678]
Target Position is [0.2345, -0.3456]
Initial Kappas are [3.14, 7.85, 11.2]
Goal Kappas are [1.57, 9.42, 13.8]
```

## 💾 模型管理

### 自动保存
训练过程中模型自动保存到：
- `experiment/checkpoint_actor.pth`
- `experiment/checkpoint_critic.pth`

### 手动保存到特定目录
训练完成后，将模型复制到对应目录：
```bash
# 例如：保存到fixed_goal/reward_step_minus_weighted_euclidean/model/
mkdir -p fixed_goal/reward_step_minus_weighted_euclidean/model/
cp experiment/checkpoint_actor.pth fixed_goal/reward_step_minus_weighted_euclidean/model/
cp experiment/checkpoint_critic.pth fixed_goal/reward_step_minus_weighted_euclidean/model/
```

## 📈 结果分析

### 训练结果文件
- `experiment/scores.pickle`: 每回合分数
- `experiment/avg_reward_list.pickle`: 平均奖励列表
- `experiment/results_train.txt`: 训练日志
- `experiment/errors.txt`: 错误日志

### 可视化
训练完成后自动生成：
- 奖励曲线图
- 平均奖励曲线图

## 🎯 使用建议

### 1. 首次使用
```bash
# 1. 使用默认配置开始小规模训练
python ddpg.py  # TRAIN=True, n_episodes=50

# 2. 观察训练效果，调整参数
# 3. 进行完整训练
```

### 2. 参数调优
- 如果收敛慢：增加 `n_episodes`
- 如果不稳定：调整学习率 (`LR_ACTOR`, `LR_CRITIC`)
- 如果过拟合：增加 `WEIGHT_DECAY`

### 3. 多实验管理
为不同实验创建不同的配置：
```bash
cp config.yaml config_experiment1.yaml
# 修改配置文件
# 运行实验并保存结果到不同目录
```

## 🔍 故障排除

### 常见问题
1. **CUDA内存不足**: 减少 `BATCH_SIZE`
2. **训练不收敛**: 调整奖励函数或学习率
3. **模型加载失败**: 检查模型文件路径

### 调试模式
取消注释第69-78行的调试代码来查看详细信息：
```python
print("Episode Number {0} and {1}th action".format(i_episode,t))
print("Goal Position",state[2:4])
print("Action: {0},  Kappas {1}".format(action, [env.kappa1,env.kappa2,env.kappa3]))
print("Reward is ", reward)
```

## 📞 支持

如有问题，请检查：
1. 依赖是否正确安装
2. CUDA是否可用
3. 配置文件格式是否正确
4. 模型文件路径是否存在
