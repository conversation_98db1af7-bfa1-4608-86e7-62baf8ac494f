"""
测试修复后的DDPG训练脚本
Test the fixed DDPG training script
"""

import sys
import os

# 添加路径
current_dir = os.getcwd()
parent_dir = os.path.dirname(current_dir)
sys.path.append(os.path.join(parent_dir, 'Reinforcement Learning'))
sys.path.append(parent_dir)

import torch
import numpy as np

from ddpg_agent import Agent
from concentric_tube_env import ConcentricTubeEnv

def test_environment_setup():
    """测试环境设置"""
    print("=" * 50)
    print("测试环境设置")
    print("=" * 50)
    
    try:
        # 创建环境
        env = ConcentricTubeEnv(
            max_extension_velocity=2.0,
            max_rotation_velocity=0.05,
            position_tolerance=8.0,
            max_steps=150
        )
        
        # 测试重置
        obs = env.reset()
        print(f"✅ 环境重置成功")
        print(f"   观测空间维度: {obs.shape}")
        print(f"   动作空间维度: {env.action_space.shape}")
        
        # 测试一步动作
        action = env.action_space.sample()
        next_obs, reward, done, info = env.step(action)
        print(f"✅ 动作执行成功")
        print(f"   奖励: {reward:.2f}")
        print(f"   完成: {done}")
        print(f"   信息: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境测试失败: {e}")
        return False

def test_agent_setup():
    """测试智能体设置"""
    print("=" * 50)
    print("测试智能体设置")
    print("=" * 50)
    
    try:
        # 创建智能体
        agent = Agent(state_size=12, action_size=6, random_seed=42)
        print(f"✅ 智能体创建成功")
        
        # 测试动作选择
        dummy_state = np.random.randn(12)
        action = agent.act(dummy_state)
        print(f"✅ 动作选择成功")
        print(f"   动作维度: {action.shape}")
        print(f"   动作范围: [{action.min():.3f}, {action.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体测试失败: {e}")
        return False

def test_training_loop():
    """测试训练循环"""
    print("=" * 50)
    print("测试训练循环 (5回合)")
    print("=" * 50)
    
    try:
        # 创建环境和智能体
        env = ConcentricTubeEnv(
            max_extension_velocity=2.0,
            max_rotation_velocity=0.05,
            position_tolerance=8.0,
            max_steps=50  # 短回合用于测试
        )
        
        agent = Agent(state_size=12, action_size=6, random_seed=42)
        
        # 简短训练循环
        for episode in range(1, 6):
            state = env.reset()
            agent.reset()
            total_reward = 0
            
            for step in range(50):
                action = agent.act(state)
                action = np.clip(action, -0.5, 0.5)  # 限制动作
                
                next_state, reward, done, info = env.step(action)
                agent.step(state, action, reward, next_state, done)
                
                state = next_state
                total_reward += reward
                
                if done:
                    break
            
            success = info.get('success', False) if 'info' in locals() else False
            print(f"回合 {episode}: 奖励={total_reward:.2f}, 步数={step+1}, 成功={success}")
        
        print("✅ 训练循环测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 训练循环测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的DDPG系统...")
    print(f"使用设备: {torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')}")
    
    tests = [
        ("环境设置", test_environment_setup),
        ("智能体设置", test_agent_setup),
        ("训练循环", test_training_loop)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n正在测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！修复后的DDPG系统可以正常工作。")
        print("现在可以运行完整的训练脚本: concentric_tube_ddpg.py")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
