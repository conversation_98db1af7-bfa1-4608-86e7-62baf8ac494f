"""
测试修改后的同心管机器人环境 - 只有内管可旋转
Test the modified concentric tube robot environment - only inner tube can rotate
"""

import sys
import os

# 添加路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
rl_path = os.path.join(project_root, 'Reinforcement Learning')
sys.path.insert(0, rl_path)
sys.path.insert(0, project_root)

import torch
import numpy as np

from ddpg_agent import Agent
from concentric_tube_env import ConcentricTubeEnv

def test_environment_dimensions():
    """测试环境维度"""
    print("=" * 50)
    print("测试环境维度")
    print("=" * 50)
    
    try:
        # 创建环境
        env = ConcentricTubeEnv(
            max_extension_velocity=2.0,
            max_rotation_velocity=0.05,
            position_tolerance=8.0,
            max_steps=150
        )
        
        # 测试重置
        obs = env.reset()
        print(f"✅ 环境重置成功")
        print(f"   观测空间维度: {obs.shape} (应该是10维)")
        print(f"   动作空间维度: {env.action_space.shape} (应该是4维)")
        
        # 检查观测空间
        expected_obs_dim = 10  # [current_pos(3), target_pos(3), config(4)]
        if obs.shape[0] == expected_obs_dim:
            print(f"✅ 观测空间维度正确: {obs.shape[0]}")
        else:
            print(f"❌ 观测空间维度错误: 期望{expected_obs_dim}, 实际{obs.shape[0]}")
            return False
            
        # 检查动作空间
        expected_action_dim = 4  # [ext_vel(3), rot_vel_in(1)]
        if env.action_space.shape[0] == expected_action_dim:
            print(f"✅ 动作空间维度正确: {env.action_space.shape[0]}")
        else:
            print(f"❌ 动作空间维度错误: 期望{expected_action_dim}, 实际{env.action_space.shape[0]}")
            return False
        
        # 测试一步动作
        action = np.array([0.1, 0.2, 0.3, 0.5])  # 4维动作
        _, reward, done, info = env.step(action)
        print(f"✅ 动作执行成功")
        print(f"   奖励: {reward:.2f}")
        print(f"   完成: {done}")
        
        # 检查机器人配置
        current_rot = env.robot.current_rotations
        print(f"✅ 旋转配置检查:")
        print(f"   外管旋转: {current_rot['outer']:.3f} (应该是0.0)")
        print(f"   中管旋转: {current_rot['middle']:.3f} (应该是0.0)")
        print(f"   内管旋转: {current_rot['inner']:.3f} (可以变化)")
        
        if current_rot['outer'] == 0.0 and current_rot['middle'] == 0.0:
            print(f"✅ 外管和中管正确固定为0")
        else:
            print(f"❌ 外管或中管旋转不为0")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_compatibility():
    """测试智能体兼容性"""
    print("=" * 50)
    print("测试智能体兼容性")
    print("=" * 50)
    
    try:
        # 创建智能体 (新维度)
        agent = Agent(state_size=10, action_size=4, random_seed=42)
        print(f"✅ 智能体创建成功 (状态维度:10, 动作维度:4)")
        
        # 测试动作选择
        dummy_state = np.random.randn(10)  # 10维状态
        action = agent.act(dummy_state)
        print(f"✅ 动作选择成功")
        print(f"   动作维度: {action.shape} (应该是4)")
        print(f"   动作范围: [{action.min():.3f}, {action.max():.3f}]")
        
        if action.shape[0] == 4:
            print(f"✅ 动作维度正确")
        else:
            print(f"❌ 动作维度错误: 期望4, 实际{action.shape[0]}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rotation_constraint():
    """测试旋转约束"""
    print("=" * 50)
    print("测试旋转约束")
    print("=" * 50)
    
    try:
        env = ConcentricTubeEnv()
        
        # 重置环境
        env.reset()
        initial_rot = env.robot.current_rotations.copy()
        print(f"初始旋转: 外管={initial_rot['outer']:.3f}, 中管={initial_rot['middle']:.3f}, 内管={initial_rot['inner']:.3f}")
        
        # 执行多个动作，尝试旋转所有管
        for i in range(5):
            # 动作: [ext_vel_out, ext_vel_mid, ext_vel_in, rot_vel_in]
            action = np.array([0.0, 0.0, 0.0, 0.5])  # 只尝试旋转内管
            env.step(action)
            
            current_rot = env.robot.current_rotations
            print(f"步骤{i+1}: 外管={current_rot['outer']:.3f}, 中管={current_rot['middle']:.3f}, 内管={current_rot['inner']:.3f}")
            
            # 检查外管和中管是否保持为0
            if current_rot['outer'] != 0.0 or current_rot['middle'] != 0.0:
                print(f"❌ 外管或中管发生了旋转!")
                return False
        
        print(f"✅ 旋转约束正确: 只有内管可以旋转")
        return True
        
    except Exception as e:
        print(f"❌ 旋转约束测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修改后的同心管机器人系统...")
    print("修改内容: 只有内管可以旋转，外管和中管不可旋转")
    print(f"使用设备: {torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')}")
    
    tests = [
        ("环境维度", test_environment_dimensions),
        ("智能体兼容性", test_agent_compatibility),
        ("旋转约束", test_rotation_constraint)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n正在测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！")
        print("修改成功:")
        print("- 动作空间: 6维 → 4维 (移除外管和中管旋转)")
        print("- 观测空间: 12维 → 10维 (移除外管和中管旋转状态)")
        print("- 旋转约束: 只有内管可以旋转")
        print("\n现在可以运行修改后的训练脚本!")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
