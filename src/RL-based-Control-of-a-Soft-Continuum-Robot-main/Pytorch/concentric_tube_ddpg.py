"""
同心管机器人DDPG训练脚本
Concentric Tube Robot DDPG Training Script

使用DDPG算法训练三管同心管机器人进行位置控制
"""

import sys
import os

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
project_root = os.path.dirname(script_dir)

# 添加必要的路径
rl_path = os.path.join(project_root, 'Reinforcement Learning')
sys.path.insert(0, rl_path)
sys.path.insert(0, project_root)

print(f"脚本目录: {script_dir}")
print(f"项目根目录: {project_root}")
print(f"RL路径: {rl_path}")
print(f"RL路径存在: {os.path.exists(rl_path)}")
print(f"concentric_tube_env.py存在: {os.path.exists(os.path.join(rl_path, 'concentric_tube_env.py'))}")

import torch
import pickle
import numpy as np
from collections import deque
import time
import yaml

# 导入DDPG相关模块
from ddpg_agent import Agent
from concentric_tube_env import ConcentricTubeEnv

print(f"使用设备: {torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')}")

# 读取配置文件
dir_path = os.path.dirname(os.path.realpath(__file__))
file_path = os.path.join(dir_path, "config.yaml")

try:
    with open(file_path, "r") as file:
        config = yaml.safe_load(file)
    print("配置文件加载成功")
except FileNotFoundError:
    print("配置文件未找到，使用默认配置")
    config = {}

# 训练参数
TRAIN = True  # 设置为 True 开始训练，False 仅加载模型
SAVE_MODELS = True  # 是否保存模型
RENDER_TRAINING = False  # 训练时是否渲染（会显著降低训练速度）

# 创建环境和智能体
print("初始化同心管机器人环境...")
env = ConcentricTubeEnv(
    max_extension_velocity=3.0,  # 降低速度，更稳定
    max_rotation_velocity=0.05,  # 降低旋转速度
    position_tolerance=5.0,      # 增大容差，更容易成功
    max_steps=200                # 减少最大步数
)

print("初始化DDPG智能体...")
# 状态空间：10维 [current_pos(3), target_pos(3), current_config(4)]
# 动作空间：4维 [ext_vel(3), rot_vel_in(1)] - 只有内管可旋转
agent = Agent(state_size=10, action_size=4, random_seed=42)

# 测试环境
print("测试环境...")
test_obs = env.reset()
print(f"观测空间维度: {test_obs.shape}")
print(f"动作空间维度: {env.action_space.shape}")
print(f"观测范围: {env.observation_space.low[:3]} 到 {env.observation_space.high[:3]}")
test_action = env.action_space.sample()
test_obs2, test_reward, test_done, test_info = env.step(test_action)
print(f"测试奖励: {test_reward:.2f}")
print("环境测试完成！")

def ddpg_train(n_episodes=1000, max_t=300, print_every=50, save_every=100):
    """
    DDPG训练函数
    
    Parameters:
    -----------
    n_episodes : int
        训练回合数
    max_t : int
        每回合最大步数
    print_every : int
        打印间隔
    save_every : int
        保存模型间隔
    """
    scores_deque = deque(maxlen=print_every)
    scores = []
    success_rates = []
    avg_rewards = []
    
    start_time = time.time()
    
    for i_episode in range(1, n_episodes + 1):
        state = env.reset()
        agent.reset()
        score = 0
        episode_success = False
        
        for step in range(max_t):
            # 选择动作
            action = agent.act(state)

            # 执行动作
            next_state, reward, done, info = env.step(action)

            # 存储经验
            agent.step(state, action, reward, next_state, done)

            state = next_state
            score += reward

            # 检查是否成功到达目标
            if 'success' in info and info['success']:
                episode_success = True

            # 可选：渲染训练过程
            if RENDER_TRAINING and i_episode % 100 == 0:
                env.render()

            if done:
                break
        
        scores_deque.append(score)
        scores.append(score)
        
        # 记录成功情况
        episode_results = getattr(ddpg_train, 'episode_results', [])
        episode_results.append(episode_success)
        ddpg_train.episode_results = episode_results

        # 计算成功率
        if i_episode >= print_every:
            recent_results = episode_results[-print_every:]
            success_rate = sum(recent_results) / len(recent_results)
            success_rates.append(success_rate)
        else:
            success_rate = sum(episode_results) / len(episode_results) if episode_results else 0.0
            success_rates.append(success_rate)
        
        avg_rewards.append(np.mean(scores_deque))
        
        # 打印进度
        if i_episode % print_every == 0:
            elapsed_time = time.time() - start_time
            avg_score = np.mean(scores_deque)
            current_success_rate = success_rates[-1] if success_rates else 0.0

            print(f'\n回合 {i_episode:4d}/{n_episodes}')
            print(f'平均奖励: {avg_score:8.2f}')
            print(f'成功率: {current_success_rate:6.1%}')
            print(f'用时: {elapsed_time/60:.1f} 分钟')
            print(f'当前回合: {"成功" if episode_success else "失败"}')
            print('-' * 50)
        
        # 保存模型
        if SAVE_MODELS and i_episode % save_every == 0:
            torch.save(agent.actor_local.state_dict(), 
                      f'concentric_tube_actor_{i_episode}.pth')
            torch.save(agent.critic_local.state_dict(), 
                      f'concentric_tube_critic_{i_episode}.pth')
            print(f"模型已保存 (回合 {i_episode})")
    
    return scores, success_rates, avg_rewards

def test_trained_model(model_path_actor, model_path_critic, n_test_episodes=10):
    """
    测试训练好的模型
    
    Parameters:
    -----------
    model_path_actor : str
        Actor模型路径
    model_path_critic : str
        Critic模型路径
    n_test_episodes : int
        测试回合数
    """
    # 加载训练好的模型
    agent.actor_local.load_state_dict(torch.load(model_path_actor))
    agent.critic_local.load_state_dict(torch.load(model_path_critic))
    
    test_scores = []
    success_count = 0
    
    for i in range(n_test_episodes):
        state = env.reset()
        score = 0
        episode_success = False
        
        for step in range(300):
            action = agent.act(state, add_noise=False)  # 测试时不添加噪声
            next_state, reward, done, info = env.step(action)
            state = next_state
            score += reward

            if 'success' in info and info['success']:
                episode_success = True
                success_count += 1

            # 渲染测试过程
            if i < 3:  # 只渲染前3个测试回合
                env.render()

            if done:
                break
        
        test_scores.append(score)
        print(f"测试回合 {i+1}: 奖励 = {score:.2f}, {'成功' if episode_success else '失败'}")
    
    print(f"\n测试结果:")
    print(f"平均奖励: {np.mean(test_scores):.2f}")
    print(f"成功率: {success_count/n_test_episodes:.1%}")

def plot_training_results(scores, success_rates, avg_rewards):
    """绘制训练结果"""
    import matplotlib.pyplot as plt

    _, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 奖励曲线
    axes[0, 0].plot(scores)
    axes[0, 0].set_title('Training Rewards')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].grid(True)

    # 平均奖励
    axes[0, 1].plot(avg_rewards)
    axes[0, 1].set_title('Average Rewards')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Average Reward')
    axes[0, 1].grid(True)

    # 成功率
    axes[1, 0].plot(success_rates)
    axes[1, 0].set_title('Success Rate')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Success Rate')
    axes[1, 0].grid(True)

    # 最近100回合的奖励分布
    if len(scores) >= 100:
        recent_scores = scores[-100:]
        axes[1, 1].hist(recent_scores, bins=20, alpha=0.7)
        axes[1, 1].set_title('Recent 100 Episodes Reward Distribution')
        axes[1, 1].set_xlabel('Reward')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig('concentric_tube_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    if TRAIN:
        print("开始训练同心管机器人...")
        print("=" * 60)
        
        # 开始训练
        scores, success_rates, avg_rewards = ddpg_train(
            n_episodes=1000,
            max_t=300,
            print_every=50,
            save_every=100
        )
        
        # 保存训练结果
        training_data = {
            'scores': scores,
            'success_rates': success_rates,
            'avg_rewards': avg_rewards
        }
        
        with open('concentric_tube_training_data.pkl', 'wb') as f:
            pickle.dump(training_data, f)
        
        print("\n训练完成！")
        print("训练数据已保存到 'concentric_tube_training_data.pkl'")
        
        # 绘制训练结果
        plot_training_results(scores, success_rates, avg_rewards)
        
    else:
        print("加载预训练模型进行测试...")
        # 测试模式 - 需要指定模型路径
        actor_path = 'concentric_tube_actor_1000.pth'
        critic_path = 'concentric_tube_critic_1000.pth'
        
        if os.path.exists(actor_path) and os.path.exists(critic_path):
            test_trained_model(actor_path, critic_path, n_test_episodes=10)
        else:
            print(f"模型文件未找到: {actor_path} 或 {critic_path}")
            print("请先训练模型或检查文件路径")
