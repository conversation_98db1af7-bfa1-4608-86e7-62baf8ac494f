"""
简化的同心管机器人DDPG训练脚本
Simplified Concentric Tube Robot DDPG Training Script

修复了训练问题的简化版本
"""

import sys
import os

# 添加路径
current_dir = os.getcwd()
parent_dir = os.path.dirname(current_dir)
sys.path.append(os.path.join(parent_dir, 'Reinforcement Learning'))
sys.path.append(parent_dir)

import torch
import numpy as np
from collections import deque
import matplotlib.pyplot as plt
import time

from ddpg_agent import Agent
from concentric_tube_env import ConcentricTubeEnv

print(f"使用设备: {torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')}")

def test_environment():
    """测试环境是否正常工作"""
    print("=" * 50)
    print("环境测试")
    print("=" * 50)
    
    env = ConcentricTubeEnv(
        max_extension_velocity=2.0,
        max_rotation_velocity=0.05,
        position_tolerance=8.0,  # 更大的容差
        max_steps=150
    )
    
    # 测试重置
    obs = env.reset()
    print(f"观测空间维度: {obs.shape}")
    print(f"观测值范围: [{obs.min():.2f}, {obs.max():.2f}]")
    
    # 测试几步动作
    total_reward = 0
    for step in range(10):
        action = env.action_space.sample() * 0.1  # 小动作
        obs, reward, done, info = env.step(action)
        total_reward += reward
        print(f"步骤 {step+1}: 奖励={reward:.2f}, 完成={done}")
        if done:
            break
    
    print(f"总奖励: {total_reward:.2f}")
    print("环境测试完成！")
    return env

def simple_train():
    """简化的训练函数"""
    print("=" * 50)
    print("开始简化训练")
    print("=" * 50)
    
    # 创建环境
    env = ConcentricTubeEnv(
        max_extension_velocity=2.0,
        max_rotation_velocity=0.05,
        position_tolerance=8.0,
        max_steps=150
    )
    
    # 创建智能体
    agent = Agent(state_size=12, action_size=6, random_seed=42)
    
    # 训练参数
    n_episodes = 500
    max_t = 150
    print_every = 25
    
    scores = []
    scores_deque = deque(maxlen=print_every)
    success_count = 0
    
    start_time = time.time()
    
    for i_episode in range(1, n_episodes + 1):
        state = env.reset()
        agent.reset()
        score = 0
        
        for t in range(max_t):
            # 选择动作 (添加动作缩放)
            action = agent.act(state)
            action = np.clip(action, -0.5, 0.5)  # 限制动作幅度
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            agent.step(state, action, reward, next_state, done)
            
            state = next_state
            score += reward
            
            if done:
                if 'success' in info and info['success']:
                    success_count += 1
                break
        
        scores_deque.append(score)
        scores.append(score)
        
        # 打印进度
        if i_episode % print_every == 0:
            elapsed_time = time.time() - start_time
            avg_score = np.mean(scores_deque)
            recent_success_rate = success_count / i_episode
            
            print(f'回合 {i_episode:3d}/{n_episodes} | '
                  f'平均奖励: {avg_score:7.2f} | '
                  f'成功率: {recent_success_rate:5.1%} | '
                  f'用时: {elapsed_time/60:.1f}分钟')
        
        # 保存模型
        if i_episode % 100 == 0:
            torch.save(agent.actor_local.state_dict(), 
                      f'simple_actor_{i_episode}.pth')
            torch.save(agent.critic_local.state_dict(), 
                      f'simple_critic_{i_episode}.pth')
    
    return scores, env, agent

def test_policy(env, agent, n_episodes=5):
    """测试训练好的策略"""
    print("=" * 50)
    print("测试训练好的策略")
    print("=" * 50)
    
    test_scores = []
    success_count = 0
    
    for i in range(n_episodes):
        state = env.reset()
        score = 0
        
        for t in range(150):
            action = agent.act(state, add_noise=False)
            action = np.clip(action, -0.5, 0.5)
            state, reward, done, info = env.step(action)
            score += reward
            
            if done:
                if 'success' in info and info['success']:
                    success_count += 1
                    print(f"测试 {i+1}: 成功! 奖励={score:.2f}, 步数={t+1}")
                else:
                    print(f"测试 {i+1}: 失败. 奖励={score:.2f}, 步数={t+1}")
                break
        
        test_scores.append(score)
    
    print(f"\n测试结果:")
    print(f"平均奖励: {np.mean(test_scores):.2f}")
    print(f"成功率: {success_count/n_episodes:.1%}")

def plot_results(scores):
    """绘制训练结果"""
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(scores)
    plt.title('训练奖励曲线')
    plt.xlabel('回合')
    plt.ylabel('奖励')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    # 计算移动平均
    window = 50
    if len(scores) >= window:
        moving_avg = []
        for i in range(window-1, len(scores)):
            moving_avg.append(np.mean(scores[i-window+1:i+1]))
        plt.plot(range(window-1, len(scores)), moving_avg)
        plt.title(f'移动平均奖励 (窗口={window})')
        plt.xlabel('回合')
        plt.ylabel('平均奖励')
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('simple_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 1. 测试环境
    test_env = test_environment()
    
    # 2. 训练
    scores, env, agent = simple_train()
    
    # 3. 测试策略
    test_policy(env, agent)
    
    # 4. 绘制结果
    plot_results(scores)
    
    print("\n训练完成！")
    print("检查生成的图片: simple_training_results.png")
