# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/RLcontrol/src'
whitelisted_packages = ''.split(';') if '' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/realsense/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/realsense/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic' != '' else []
