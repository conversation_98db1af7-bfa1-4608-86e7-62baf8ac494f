Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_0c19f/fast && /usr/bin/make -f CMakeFiles/cmTC_0c19f.dir/build.make CMakeFiles/cmTC_0c19f.dir/build
make[1]: 进入目录“/home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_0c19f.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_0c19f.dir/src.c.o   -c /home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_0c19f
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0c19f.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_0c19f.dir/src.c.o  -o cmTC_0c19f 
/usr/bin/ld: CMakeFiles/cmTC_0c19f.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_0c19f.dir/build.make:87：cmTC_0c19f] 错误 1
make[1]: 离开目录“/home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_0c19f/fast] 错误 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_7ffca/fast && /usr/bin/make -f CMakeFiles/cmTC_7ffca.dir/build.make CMakeFiles/cmTC_7ffca.dir/build
make[1]: 进入目录“/home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_7ffca.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_7ffca.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_7ffca
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7ffca.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_7ffca.dir/CheckFunctionExists.c.o  -o cmTC_7ffca  -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_7ffca.dir/build.make:87：cmTC_7ffca] 错误 1
make[1]: 离开目录“/home/<USER>/RLcontrol/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:121：cmTC_7ffca/fast] 错误 2



