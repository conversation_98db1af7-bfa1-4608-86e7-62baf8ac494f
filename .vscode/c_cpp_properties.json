{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/realsense/devel/include/**", "/home/<USER>/catkin_ws/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/catkin_ws/src/atr_pkg/include/**", "/home/<USER>/realsense/src/ddynamic_reconfigure/include/**", "/home/<USER>/realsense/src/realsense-ros/realsense2_camera/include/**", "/home/<USER>/catkin_ws/src/ssr_pkg/include/**", "/home/<USER>/catkin_ws/src/wpr_simulation/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}